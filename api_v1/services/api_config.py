"""
Unified API Configuration Service

Consolidates functionality from api_config_service.py and api_service.py
into a single, well-organized service with clear separation of concerns.
"""

from __future__ import annotations

import asyncio
import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from database.connection import get_collection
from models.base import now_utc
from models.api import (
    APIConfiguration,
    APIEndpoint,
    APIHealthStatus,
    APIUsageMetrics,
    APIRequestLog,
    APICredential,
    APIAuditLog,
    AuthenticationType,
    APIEnvironment,
    APIStatus,
    HTTPMethod,
)
from config.settings import get_settings
from utils.validation import ValidationError
from ..utils.encryption import EncryptionService
from ..core.exceptions import APIConfigurationError

logger = logging.getLogger(__name__)


class ConfigSource(str, Enum):
    """Configuration source priority"""

    ADMIN_PANEL = "admin_panel"
    DATABASE = "database"
    ENVIRONMENT = "environment"
    DEFAULT = "default"


@dataclass
class APIEndpointConfig:
    """API endpoint configuration"""

    name: str
    url: str
    method: str = "GET"
    timeout: int = 30
    retry_count: int = 3
    retry_delays: List[int] = None

    def __post_init__(self):
        if self.retry_delays is None:
            self.retry_delays = [1, 2, 4]


@dataclass
class APICredentials:
    """API authentication credentials"""

    login_token: str = ""
    session_cookies: Dict[str, str] = None
    headers: Dict[str, str] = None
    auth_profile_id: Optional[str] = None
    use_profile: bool = False
    credential_overrides: Dict[str, Any] = None

    def __post_init__(self):
        if self.session_cookies is None:
            self.session_cookies = {}
        if self.headers is None:
            self.headers = {}
        if self.credential_overrides is None:
            self.credential_overrides = {}


@dataclass
class UnifiedAPIConfiguration:
    """Complete API configuration combining both legacy formats"""

    service_name: str
    base_url: str
    endpoints: Dict[str, APIEndpointConfig]
    credentials: APICredentials
    enabled: bool = True
    last_updated: datetime = None
    source: ConfigSource = ConfigSource.DEFAULT
    display_name: str = ""
    description: str = ""
    category: str = "general"
    tags: List[str] = None
    environment: str = "development"
    version: str = "1.0"
    health_check_endpoint: str = ""
    documentation_url: str = ""

    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.last_updated is None:
            self.last_updated = now_utc()


class UnifiedAPIConfigurationService:
    """
    Unified API Configuration Service

    Consolidates functionality from both legacy services:
    - api_config_service.py (dataclass-based, admin UI focused)
    - api_service.py (Pydantic-based, database focused)

    Provides a single interface for all API configuration operations.
    """

    def __init__(self):
        self.settings = get_settings()
        self.encryption = EncryptionService()

        # Database collections
        self.api_configs = get_collection("api_configurations")
        self.api_endpoints = get_collection("api_endpoints")
        self.api_health = get_collection("api_health_status")
        self.api_metrics = get_collection("api_usage_metrics")
        self.api_logs = get_collection("api_request_logs")
        self.api_credentials = get_collection("api_credentials")
        self.api_audit = get_collection("api_audit_logs")

        # Configuration cache and locks
        self._config_cache = {}
        self._config_lock = asyncio.Lock()
        self._cache_ttl = 300  # 5 minutes

    async def get_api_config_by_name(
        self, name: str, decrypt_sensitive: bool = False
    ) -> Optional[APIConfiguration]:
        """Fetch an API configuration by its name (case-insensitive).

        Provides compatibility with legacy code that retrieved configs by name.
        """
        try:
            # Prefer an exact, case-insensitive match via query
            query = {"name": {"$regex": f"^{name}$", "$options": "i"}, "is_deleted": {"$ne": True}}
            doc = await self.api_configs.find_one(query)
            if not doc:
                # Fallback: search using list_api_configs
                configs, _ = await self.list_api_configs(page=1, per_page=50, search=name)
                match = next((c for c in configs if (c.name or "").lower() == name.lower()), None)
                if not match:
                    return None
                # Fetch full config by ID to apply decryption/caching behavior
                return await self.get_api_config(str(match.id), decrypt_sensitive=decrypt_sensitive)

            api_config = APIConfiguration.from_mongo(doc)
            if decrypt_sensitive and api_config.authentication:
                api_config.authentication = self._decrypt_auth_config(api_config.authentication)
            return api_config
        except Exception as e:
            logger.error(f"Failed to get API configuration by name '{name}': {e}")
            return None

    async def create_api_config(
        self, config_data: Dict[str, Any], created_by: str
    ) -> APIConfiguration:
        """Create a new API configuration"""
        try:
            # Encrypt sensitive authentication data
            auth_config = config_data.get("authentication", {})
            if auth_config:
                auth_config = self._encrypt_auth_config(auth_config)
                config_data["authentication"] = auth_config

            # Set system fields
            config_data["created_by"] = created_by
            config_data["created_at"] = now_utc()

            # Create and validate the configuration
            api_config = APIConfiguration(**config_data)

            # Insert into database
            result = await self.api_configs.insert_one(api_config.to_mongo())
            api_config.id = result.inserted_id

            # Create initial health status
            await self._create_initial_health_status(str(api_config.id))

            # Log the creation
            await self._audit_log(
                operation="create",
                resource_type="api_config",
                resource_id=str(api_config.id),
                actor_id=created_by,
                new_values={"name": api_config.name, "base_url": api_config.base_url},
                success=True,
            )

            # Clear cache
            self._config_cache.clear()

            logger.info(
                f"Created API configuration: {api_config.name} (ID: {api_config.id})"
            )
            return api_config

        except Exception as e:
            logger.error(f"Failed to create API configuration: {e}")
            await self._audit_log(
                operation="create",
                resource_type="api_config",
                resource_id="",
                actor_id=created_by,
                new_values=config_data.get("name", "unknown"),
                success=False,
                error_message=str(e),
            )
            raise APIConfigurationError(f"Failed to create API configuration: {e}")

    async def get_api_config(
        self, config_id: str, decrypt_sensitive: bool = False
    ) -> Optional[APIConfiguration]:
        """Get API configuration by ID"""
        try:
            # Check cache first
            cache_key = f"{config_id}_{str(decrypt_sensitive).lower()}"
            if cache_key in self._config_cache:
                cached_config, cached_time = self._config_cache[cache_key]
                if (datetime.now() - cached_time).seconds < self._cache_ttl:
                    return cached_config

            # Fetch from database
            config_doc = await self.api_configs.find_one({"_id": config_id})
            if not config_doc:
                return None

            # Convert to Pydantic model
            api_config = APIConfiguration.from_mongo(config_doc)

            # Decrypt sensitive data if requested
            if decrypt_sensitive and api_config.authentication:
                decrypted_auth = self._decrypt_auth_config(api_config.authentication)
                api_config.authentication = decrypted_auth

            # Cache the result
            self._config_cache[cache_key] = (api_config, datetime.now())

            return api_config

        except Exception as e:
            logger.error(f"Failed to get API configuration {config_id}: {e}")
            return None

    async def list_api_configs(
        self,
        page: int = 1,
        per_page: int = 20,
        search: Optional[str] = None,
        status: Optional[APIStatus] = None,
        environment: Optional[APIEnvironment] = None,
    ) -> Tuple[List[APIConfiguration], int]:
        """List API configurations with filtering and pagination"""
        try:
            # Build query
            query = {}
            if search:
                query["$or"] = [
                    {"name": {"$regex": search, "$options": "i"}},
                    {"description": {"$regex": search, "$options": "i"}},
                    {"base_url": {"$regex": search, "$options": "i"}},
                ]
            if status:
                query["status"] = status.value
            if environment:
                query["environment"] = environment.value

            # Get total count
            total = await self.api_configs.count_documents(query)

            # Get paginated results
            skip = (page - 1) * per_page
            cursor = self.api_configs.find(query).skip(skip).limit(per_page)

            configs = []
            # Support both Motor cursors and in-memory simulation cursors
            try:
                docs = await cursor.to_list(length=per_page)
            except TypeError:
                # Fallback for any cursor lacking to_list signature
                docs = []
            for doc in docs:
                try:
                    config = APIConfiguration.from_mongo(doc)
                    configs.append(config)
                except Exception as e:
                    logger.warning(f"Failed to parse config document: {e}")
                    continue

            return configs, total

        except Exception as e:
            logger.error(f"Failed to list API configurations: {e}")
            return [], 0

    def _encrypt_auth_config(self, auth_config: Dict[str, Any]) -> Dict[str, Any]:
        """Encrypt sensitive fields in authentication configuration"""
        encrypted_config = auth_config.copy()

        # Fields that need encryption
        sensitive_fields = [
            "api_key",
            "bearer_token",
            "password",
            "oauth2_client_secret",
        ]

        for field in sensitive_fields:
            if field in encrypted_config and encrypted_config[field]:
                encrypted_config[field] = self.encryption.encrypt(
                    encrypted_config[field]
                )

        return encrypted_config

    def _decrypt_auth_config(self, auth_config) -> Dict[str, Any]:
        """Decrypt sensitive fields in authentication configuration"""
        if hasattr(auth_config, "dict"):
            decrypted_config = auth_config.dict()
        else:
            decrypted_config = dict(auth_config)

        # Fields that need decryption
        sensitive_fields = [
            "api_key",
            "bearer_token",
            "password",
            "oauth2_client_secret",
        ]

        for field in sensitive_fields:
            if field in decrypted_config and decrypted_config[field]:
                decrypted_config[field] = self.encryption.decrypt(
                    decrypted_config[field]
                )

        return decrypted_config

    async def update_api_config(
        self, config_id: str, update_data: Dict[str, Any], updated_by: str
    ) -> bool:
        """Update an existing API configuration"""
        try:
            # Get existing config for audit trail
            existing_config = await self.get_api_config(config_id)
            if not existing_config:
                raise APIConfigurationError(f"API configuration {config_id} not found")

            # Encrypt sensitive authentication data if present
            if "authentication" in update_data:
                update_data["authentication"] = self._encrypt_auth_config(
                    update_data["authentication"]
                )

            # Set system fields
            update_data["updated_by"] = updated_by
            update_data["updated_at"] = now_utc()

            # Update in database
            result = await self.api_configs.update_one(
                {"_id": config_id}, {"$set": update_data}
            )

            if result.modified_count > 0:
                # Log the update
                await self._audit_log(
                    operation="update",
                    resource_type="api_config",
                    resource_id=config_id,
                    actor_id=updated_by,
                    old_values={"name": existing_config.name},
                    new_values=update_data,
                    success=True,
                )

                # Clear cache
                self._config_cache.clear()

                logger.info(f"Updated API configuration: {config_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to update API configuration {config_id}: {e}")
            await self._audit_log(
                operation="update",
                resource_type="api_config",
                resource_id=config_id,
                actor_id=updated_by,
                success=False,
                error_message=str(e),
            )
            raise APIConfigurationError(f"Failed to update API configuration: {e}")

    async def delete_api_config(self, config_id: str, deleted_by: str) -> bool:
        """Delete an API configuration (soft delete)"""
        try:
            # Get existing config for audit trail
            existing_config = await self.get_api_config(config_id)
            if not existing_config:
                return False

            # Soft delete
            result = await self.api_configs.update_one(
                {"_id": config_id},
                {
                    "$set": {
                        "deleted_at": now_utc(),
                        "deleted_by": deleted_by,
                        "status": APIStatus.INACTIVE.value,
                    }
                },
            )

            if result.modified_count > 0:
                # Log the deletion
                await self._audit_log(
                    operation="delete",
                    resource_type="api_config",
                    resource_id=config_id,
                    actor_id=deleted_by,
                    old_values={"name": existing_config.name},
                    success=True,
                )

                # Clear cache
                self._config_cache.clear()

                logger.info(f"Deleted API configuration: {config_id}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to delete API configuration {config_id}: {e}")
            await self._audit_log(
                operation="delete",
                resource_type="api_config",
                resource_id=config_id,
                actor_id=deleted_by,
                success=False,
                error_message=str(e),
            )
            return False

    async def get_default_config(
        self, service_name: str
    ) -> Optional[UnifiedAPIConfiguration]:
        """Get default configuration for a service"""
        defaults = {
            "api1": UnifiedAPIConfiguration(
                service_name="api1",
                base_url="https://ronaldo-club.to/api",
                endpoints={
                    "list_items": APIEndpointConfig(
                        "list_items",
                        "https://ronaldo-club.to/api/cards/hq/list",
                        "POST",
                        30,
                    ),
                    "cart_view": APIEndpointConfig(
                        "cart_view", "https://ronaldo-club.to/api/cart/", "GET", 30
                    ),
                    "cart_add": APIEndpointConfig(
                        "cart_add", "https://ronaldo-club.to/api/cart/", "POST", 30
                    ),
                    "cart_remove": APIEndpointConfig(
                        "cart_remove", "https://ronaldo-club.to/api/cart/", "DELETE", 30
                    ),
                    "user_info": APIEndpointConfig(
                        "user_info", "https://ronaldo-club.to/api/user/getme", "GET", 30
                    ),
                    "checkout": APIEndpointConfig(
                        "checkout", "https://ronaldo-club.to/api/checkout/", "POST", 60
                    ),
                },
                credentials=APICredentials(),
                source=ConfigSource.DEFAULT,
                display_name="API 1 - External Cart API",
                description="API 1: External API for cart operations, item listing, and user management (Ronaldo Club)",
                category="ecommerce",
                tags=["api1", "cart", "external", "api", "ecommerce", "ronaldo"],
                environment="development",
                version="1.0",
                health_check_endpoint="/user/getme",
                documentation_url="https://ronaldo-club.to/api/docs",
            )
        }
        return defaults.get(service_name)

    async def _create_initial_health_status(self, config_id: str):
        """Create initial health status for a new API configuration"""
        try:
            health_status = APIHealthStatus(
                api_config_id=config_id,
                is_healthy=False,
                last_check_at=now_utc(),
                next_check_at=now_utc() + timedelta(minutes=5),
            )
            await self.api_health.insert_one(health_status.to_mongo())
        except Exception as e:
            logger.warning(
                f"Failed to create initial health status for {config_id}: {e}"
            )

    async def _audit_log(
        self,
        operation: str,
        resource_type: str,
        resource_id: str,
        actor_id: str,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        success: bool = True,
        error_message: Optional[str] = None,
    ):
        """Create an audit log entry"""
        try:
            audit_entry = APIAuditLog(
                operation=operation,
                resource_type=resource_type,
                resource_id=resource_id,
                actor_id=actor_id,
                old_values=old_values or {},
                new_values=new_values or {},
                success=success,
                error_message=error_message,
                timestamp=now_utc(),
            )
            await self.api_audit.insert_one(audit_entry.to_mongo())
        except Exception as e:
            logger.warning(f"Failed to create audit log entry: {e}")


# Singleton instance
_unified_service_instance = None


def get_unified_api_config_service() -> UnifiedAPIConfigurationService:
    """Get singleton instance of the unified API configuration service"""
    global _unified_service_instance
    if _unified_service_instance is None:
        _unified_service_instance = UnifiedAPIConfigurationService()
    return _unified_service_instance

# --- Compatibility aliases for smoother migration --------------------------------

# Allow importing these names from this module, matching legacy imports
# Old: from services.api_service import APIConfigurationService
# New: from api_v1.services.api_config import APIConfigurationService
APIConfigurationService = UnifiedAPIConfigurationService

# Old helper: from services.api_config_service import get_api_config_service
# New: expose the same name pointing to unified service getter
def get_api_config_service() -> UnifiedAPIConfigurationService:
    return get_unified_api_config_service()

# Old dataclass names sometimes imported from services.api_config_service
# Re-export equivalents for typing compatibility
APIEndpoint = APIEndpointConfig
