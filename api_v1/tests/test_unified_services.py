"""
Test Suite for Unified API v1 Services

Tests the core functionality of the unified API services to ensure
they work correctly after consolidation and refactoring.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

# Import the unified services
from api_v1.services.api_config import (
    UnifiedAPIConfigurationService,
    get_unified_api_config_service,
    UnifiedAPIConfiguration,
    APICredentials,
    ConfigSource,
)
from api_v1.services.http_client import (
    UnifiedHTTPClient,
    get_http_client,
    HTTPRequest,
    HTTPMethod,
    HTTPResponse,
)
from api_v1.utils.encryption import EncryptionService, get_encryption_service
from api_v1.utils.authentication import Authenti<PERSON><PERSON><PERSON><PERSON>, get_auth_helper
from api_v1.utils.error_handling import UnifiedErrorHandler, get_error_handler
from api_v1.core.exceptions import APIConfigurationError, AuthenticationError


class TestUnifiedAPIConfigurationService:
    """Test the unified API configuration service"""

    @pytest.fixture
    def service(self):
        """Create a test service instance"""
        return UnifiedAPIConfigurationService()

    @pytest.fixture
    def mock_collections(self, service):
        """Mock database collections"""
        service.api_configs = AsyncMock()
        service.api_health = AsyncMock()
        service.api_audit = AsyncMock()
        return service

    @pytest.mark.asyncio
    async def test_create_api_config(self, mock_collections):
        """Test creating a new API configuration"""
        # Mock database response
        mock_collections.api_configs.insert_one.return_value = MagicMock(
            inserted_id="test_id_123"
        )

        config_data = {
            "name": "test_api",
            "base_url": "https://api.test.com",
            "authentication": {
                "type": "bearer_token",
                "bearer_token": "test_token"
            }
        }

        # This would normally create an APIConfiguration object
        # For testing, we'll mock the creation
        with patch('api_v1.services.api_config.APIConfiguration') as mock_config:
            mock_instance = MagicMock()
            mock_instance.to_mongo.return_value = {"test": "data"}
            mock_config.return_value = mock_instance

            result = await mock_collections.create_api_config(config_data, "test_user")

            # Verify the configuration was created
            assert mock_collections.api_configs.insert_one.called
            assert mock_collections.api_health.insert_one.called
            assert mock_collections.api_audit.insert_one.called

    @pytest.mark.asyncio
    async def test_get_api_config_with_cache(self, mock_collections):
        """Test getting API configuration with caching"""
        # Test cache hit
        mock_collections._config_cache = {
            "test_id_false": (MagicMock(), datetime.now())
        }

        result = await mock_collections.get_api_config("test_id", decrypt_sensitive=False)
        
        # Should return cached result without database call
        assert not mock_collections.api_configs.find_one.called

    def test_get_default_config(self, service):
        """Test getting default configuration"""
        config = asyncio.run(service.get_default_config("api1"))
        
        assert config is not None
        assert config.service_name == "api1"
        assert config.base_url == "https://ronaldo-club.to/api"
        assert "list_items" in config.endpoints


class TestUnifiedHTTPClient:
    """Test the unified HTTP client"""

    @pytest.fixture
    def client(self):
        """Create a test client instance"""
        return UnifiedHTTPClient()

    @pytest.fixture
    def mock_session(self, client):
        """Mock aiohttp session"""
        client._session = AsyncMock()
        return client

    @pytest.mark.asyncio
    async def test_successful_request(self, mock_session):
        """Test successful HTTP request"""
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = '{"success": true}'
        mock_response.headers = {"content-type": "application/json"}
        
        mock_session._session.request.return_value.__aenter__.return_value = mock_response

        request_config = HTTPRequest(
            method=HTTPMethod.GET,
            url="https://api.test.com/data",
            timeout=30
        )

        response = await mock_session.request(request_config)

        assert response.success is True
        assert response.status_code == 200
        assert response.data == {"success": True}

    @pytest.mark.asyncio
    async def test_request_with_retry(self, mock_session):
        """Test HTTP request with retry logic"""
        # Mock failed then successful response
        mock_response_fail = AsyncMock()
        mock_response_fail.status = 500
        mock_response_fail.text.return_value = "Server Error"

        mock_response_success = AsyncMock()
        mock_response_success.status = 200
        mock_response_success.text.return_value = '{"success": true}'
        mock_response_success.headers = {"content-type": "application/json"}

        mock_session._session.request.return_value.__aenter__.side_effect = [
            mock_response_fail,
            mock_response_success
        ]

        request_config = HTTPRequest(
            method=HTTPMethod.GET,
            url="https://api.test.com/data",
            max_retries=2,
            retry_delays=[0.1, 0.2]  # Short delays for testing
        )

        response = await mock_session.request(request_config)

        assert response.success is True
        assert response.retry_count == 1


class TestEncryptionService:
    """Test the encryption service"""

    @pytest.fixture
    def encryption_service(self):
        """Create a test encryption service"""
        return EncryptionService()

    def test_encrypt_decrypt_cycle(self, encryption_service):
        """Test encryption and decryption cycle"""
        original_data = "sensitive_password_123"
        
        encrypted = encryption_service.encrypt(original_data)
        assert encrypted != original_data
        assert len(encrypted) > 0

        decrypted = encryption_service.decrypt(encrypted)
        assert decrypted == original_data

    def test_encrypt_empty_string(self, encryption_service):
        """Test encrypting empty string"""
        result = encryption_service.encrypt("")
        assert result == ""

    def test_decrypt_invalid_data(self, encryption_service):
        """Test decrypting invalid data"""
        result = encryption_service.decrypt("invalid_encrypted_data")
        assert result == "[DECRYPTION_FAILED]"

    def test_encrypt_dict_fields(self, encryption_service):
        """Test encrypting specific fields in a dictionary"""
        data = {
            "username": "test_user",
            "password": "secret_password",
            "api_key": "secret_key",
            "public_info": "not_secret"
        }
        
        sensitive_fields = ["password", "api_key"]
        encrypted_data = encryption_service.encrypt_dict_fields(data, sensitive_fields)
        
        assert encrypted_data["username"] == "test_user"
        assert encrypted_data["public_info"] == "not_secret"
        assert encrypted_data["password"] != "secret_password"
        assert encrypted_data["api_key"] != "secret_key"


class TestAuthenticationHelper:
    """Test the authentication helper"""

    def test_build_bearer_token_headers(self):
        """Test building bearer token headers"""
        auth_config = {
            "type": "bearer_token",
            "bearer_token": "test_token_123"
        }
        
        headers = AuthenticationHelper.build_auth_headers(auth_config)
        
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer test_token_123"

    def test_build_api_key_headers(self):
        """Test building API key headers"""
        auth_config = {
            "type": "api_key",
            "api_key": "test_key_123",
            "api_key_header": "X-API-Key"
        }
        
        headers = AuthenticationHelper.build_auth_headers(auth_config)
        
        assert "X-API-Key" in headers
        assert headers["X-API-Key"] == "test_key_123"

    def test_build_basic_auth_headers(self):
        """Test building basic auth headers"""
        auth_config = {
            "type": "basic_auth",
            "username": "test_user",
            "password": "test_pass"
        }
        
        headers = AuthenticationHelper.build_auth_headers(auth_config)
        
        assert "Authorization" in headers
        assert headers["Authorization"].startswith("Basic ")

    def test_validate_auth_config(self):
        """Test authentication configuration validation"""
        # Valid bearer token config
        valid_config = {
            "type": "bearer_token",
            "bearer_token": "test_token"
        }
        
        is_valid, error = AuthenticationHelper.validate_auth_config(valid_config)
        assert is_valid is True
        assert error is None

        # Invalid config (missing token)
        invalid_config = {
            "type": "bearer_token"
        }
        
        is_valid, error = AuthenticationHelper.validate_auth_config(invalid_config)
        assert is_valid is False
        assert "Bearer token is required" in error

    def test_mask_sensitive_data(self):
        """Test masking sensitive data"""
        data = {
            "username": "test_user",
            "password": "very_long_password_123",
            "api_key": "short",
            "public_info": "not_secret"
        }
        
        masked = AuthenticationHelper.mask_sensitive_data(data)
        
        assert masked["username"] == "test_user"
        assert masked["public_info"] == "not_secret"
        assert masked["password"] == "very...123"
        assert masked["api_key"] == "***"


class TestUnifiedErrorHandler:
    """Test the unified error handler"""

    @pytest.fixture
    def error_handler(self):
        """Create a test error handler"""
        return UnifiedErrorHandler()

    def test_categorize_api_configuration_error(self, error_handler):
        """Test categorizing API configuration errors"""
        error = APIConfigurationError("Config not found")
        error_info = error_handler.categorize_error(error)
        
        assert error_info.category.value == "configuration"
        assert error_info.severity.value == "high"

    def test_categorize_authentication_error(self, error_handler):
        """Test categorizing authentication errors"""
        error = AuthenticationError("Invalid credentials")
        error_info = error_handler.categorize_error(error)
        
        assert error_info.category.value == "authentication"
        assert error_info.severity.value == "high"

    def test_categorize_timeout_error(self, error_handler):
        """Test categorizing timeout errors"""
        error = Exception("Connection timeout occurred")
        error_info = error_handler.categorize_error(error)
        
        assert error_info.code == "CONNECTION_TIMEOUT"
        assert error_info.category.value == "network"

    def test_format_error_message_html(self, error_handler):
        """Test formatting error message in HTML format"""
        error = Exception("Test error")
        error_info = error_handler.categorize_error(error)
        
        message = error_handler.format_error_message(error_info, format_type="html")
        
        assert "<b>" in message
        assert "💡" in message
        assert error_info.title in message

    def test_format_error_message_plain(self, error_handler):
        """Test formatting error message in plain text format"""
        error = Exception("Test error")
        error_info = error_handler.categorize_error(error)
        
        message = error_handler.format_error_message(error_info, format_type="plain")
        
        assert "<b>" not in message
        assert error_info.title in message

    def test_should_retry_logic(self, error_handler):
        """Test retry logic for different error types"""
        # Network error should be retryable
        network_error = Exception("Connection timeout")
        network_info = error_handler.categorize_error(network_error)
        assert error_handler.should_retry(network_info) is True

        # Authentication error should not be retryable
        auth_error = AuthenticationError("Invalid credentials")
        auth_info = error_handler.categorize_error(auth_error)
        assert error_handler.should_retry(auth_info) is False


# Integration tests
class TestServiceIntegration:
    """Test integration between services"""

    @pytest.mark.asyncio
    async def test_config_service_with_encryption(self):
        """Test API config service with encryption integration"""
        encryption_service = get_encryption_service()
        
        # Test that sensitive data gets encrypted
        auth_config = {
            "type": "bearer_token",
            "bearer_token": "test_token_123"
        }
        
        # This would normally be done by the config service
        encrypted_config = encryption_service.encrypt_dict_fields(
            auth_config, 
            ["bearer_token"]
        )
        
        assert encrypted_config["bearer_token"] != "test_token_123"
        
        # Decrypt and verify
        decrypted_config = encryption_service.decrypt_dict_fields(
            encrypted_config,
            ["bearer_token"]
        )
        
        assert decrypted_config["bearer_token"] == "test_token_123"

    def test_http_client_with_auth_helper(self):
        """Test HTTP client with authentication helper integration"""
        auth_helper = get_auth_helper()
        
        auth_config = {
            "type": "bearer_token",
            "bearer_token": "test_token"
        }
        
        headers = auth_helper.build_auth_headers(auth_config)
        
        # Verify headers are built correctly for HTTP client use
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer test_token"


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
