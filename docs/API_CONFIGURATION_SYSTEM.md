# Centralized API Configuration Management System

## Overview

The centralized API configuration management system provides a unified way to manage all external API endpoints, credentials, and settings across the entire application. This system eliminates hardcoded API configurations and enables dynamic management through the admin panel.

## Architecture

### Core Components

1. **APIConfigService** (`services/api_config_service.py`)

   - Central service for managing API configurations
   - Handles configuration loading, caching, and persistence
   - Provides encryption for sensitive credentials
   - Supports multiple configuration sources with priority

2. **Admin Panel Interface** (`handlers/admin_api_config_handlers.py`)

   - Web interface for managing API configurations
   - Real-time connection testing
   - Configuration validation and audit trails
   - Role-based access control

3. **Service Integration**
   - CheckoutQueueService: Uses centralized config for external cart operations
   - CartService: Uses centralized config for cart management
   - Future services can easily integrate with the system

## Configuration Sources & Priority

The system loads configurations from multiple sources with the following priority:

1. **Admin Panel Settings** (Highest Priority)

   - Configurations saved through the admin interface
   - Stored in database with `source: "admin_panel"`
   - Allows real-time updates without code changes

2. **Database Settings**

   - Programmatically saved configurations
   - Stored in database with `source: "database"`
   - Used for automated configuration management

3. **Environment Variables**

   - Traditional environment-based configuration
   - Loaded from `.env` file or system environment
   - Provides backward compatibility

4. **Default Values** (Lowest Priority)
   - Hardcoded fallback configurations
   - Ensures system functionality even without configuration
   - Used for initial setup and development

## Data Models

### APIConfiguration

```python
@dataclass
class APIConfiguration:
    service_name: str           # Unique service identifier
    base_url: str              # Base API URL
    endpoints: Dict[str, APIEndpoint]  # Available endpoints
    credentials: APICredentials # Authentication data
    enabled: bool              # Enable/disable flag
    last_updated: datetime     # Last modification time
    source: ConfigSource       # Configuration source
```

### APIEndpoint

```python
@dataclass
class APIEndpoint:
    name: str                  # Endpoint identifier
    url: str                   # Full endpoint URL
    method: str                # HTTP method (GET, POST, etc.)
    timeout: int               # Request timeout in seconds
    retry_count: int           # Number of retry attempts
    retry_delays: List[int]    # Retry delay intervals
```

### APICredentials

```python
@dataclass
class APICredentials:
    login_token: str                    # JWT or API token
    session_cookies: Dict[str, str]     # Session cookies
    headers: Dict[str, str]             # Request headers
```

## Security Features

### Credential Encryption

- Sensitive data (tokens, passwords) encrypted in database
- Uses Fernet symmetric encryption
- Encryption key managed through environment variables
- Automatic encryption/decryption during save/load operations

### Access Control

- Admin-only access to configuration management
- Audit trail for all configuration changes
- User identification for change tracking
- IP address and user agent logging

### Validation

- API endpoint URL validation to prevent SSRF attacks
- Configuration schema validation
- Connection testing before saving
- Rollback capability for failed configurations

## Usage Examples

### Getting API Configuration

```python
from api_v1.services.api_config import get_api_config_service

api_service = get_api_config_service()
config = await api_service.get_api_config("external_cart")

if config and config.enabled:
    # Use configuration
    session = await api_service.create_http_session("external_cart")
    # Make API calls...
```

### Service Integration

```python
class MyService:
    def __init__(self):
        self.api_config_service = get_api_config_service()
        self._api_config = None

    async def _get_api_config(self):
        if not self._api_config:
            self._api_config = await self.api_config_service.get_api_config("my_service")
        return self._api_config

    async def make_api_call(self):
        config = await self._get_api_config()
        if not config or not config.enabled:
            raise Exception("API not configured or disabled")

        endpoint_url = config.endpoints["my_endpoint"].url
        # Make API call...
```

### Admin Panel Management

```python
# Save new configuration
new_config = APIConfiguration(
    service_name="my_service",
    base_url="https://api.example.com",
    endpoints={
        "list": APIEndpoint("list", "https://api.example.com/list", "GET"),
        "create": APIEndpoint("create", "https://api.example.com/create", "POST")
    },
    credentials=APICredentials(
        login_token="your_token_here",
        headers={"Authorization": "Bearer your_token_here"}
    )
)

success = await api_service.save_api_config(new_config, ConfigSource.ADMIN_PANEL, user_id)
```

## Admin Panel Features

### Configuration Management

- **View All Configurations**: List all configured services with status
- **Edit Configuration**: Modify endpoints, credentials, and settings
- **Enable/Disable**: Toggle service availability
- **Test Connection**: Verify API connectivity and credentials

### Monitoring & Debugging

- **Connection Testing**: Real-time API connectivity verification
- **Audit Trail**: Complete history of configuration changes
- **Error Logging**: Detailed error messages and debugging info
- **Performance Metrics**: Response times and success rates

### Security Features

- **Encrypted Storage**: Sensitive credentials encrypted in database
- **Access Control**: Admin-only access with user tracking
- **Change Auditing**: Complete audit trail with user identification
- **Validation**: Input validation and security checks

## Configuration Examples

### API 1 External Cart Service

```json
{
  "service_name": "api1_external_cart",
  "base_url": "https://ronaldo-club.to/api",
  "display_name": "API 1 - External Cart API",
  "description": "API 1: External API for cart operations, item listing, and user management (Ronaldo Club)",
  "endpoints": {
    "cart_view": {
      "name": "cart_view",
      "url": "https://ronaldo-club.to/api/cart/",
      "method": "GET",
      "timeout": 30,
      "retry_count": 3,
      "retry_delays": [1, 2, 4]
    },
    "cart_add": {
      "name": "cart_add",
      "url": "https://ronaldo-club.to/api/cart/",
      "method": "POST",
      "timeout": 30,
      "retry_count": 3,
      "retry_delays": [1, 2, 4]
    },
    "cart_remove": {
      "name": "cart_remove",
      "url": "https://ronaldo-club.to/api/cart/",
      "method": "DELETE",
      "timeout": 30,
      "retry_count": 3,
      "retry_delays": [1, 2, 4]
    },
    "checkout": {
      "name": "checkout",
      "url": "https://ronaldo-club.to/api/checkout/",
      "method": "POST",
      "timeout": 60,
      "retry_count": 3,
      "retry_delays": [1, 2, 4]
    }
  },
  "credentials": {
    "login_token": "your_jwt_token_here",
    "session_cookies": {
      "__ddg1_": "your_session_cookie_here",
      "__ddg8_": "your_session_cookie_here",
      "__ddg9_": "your_ip_tracking_cookie_here",
      "__ddg10_": "your_timestamp_cookie_here",
      "_ga": "your_google_analytics_cookie_here",
      "_ga_KZWCRF57VT": "your_ga_session_cookie_here",
      "testcookie": "1"
    },
    "headers": {
      "Accept": "application/json, text/plain, */*",
      "Accept-Language": "en-US,en;q=0.9",
      "Content-Type": "application/json",
      "Origin": "https://ronaldo-club.to",
      "Referer": "https://ronaldo-club.to/store/cart",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36..."
    }
  },
  "enabled": true,
  "source": "admin_panel"
}
```

## Environment Variables

### Required Variables

```bash
# API Configuration Encryption Key
API_CONFIG_ENCRYPTION_KEY=<fernet_key_here>

# External API Credentials (fallback)
EXTERNAL_LOGIN_TOKEN=your_jwt_token_here
EXTERNAL_DDG1=your_session_cookie_here
EXTERNAL_DDG8=your_session_cookie_here
EXTERNAL_DDG9=your_ip_tracking_cookie_here
EXTERNAL_DDG10=your_timestamp_cookie_here
EXTERNAL_GA=your_google_analytics_cookie_here
EXTERNAL_GA_KZWCRF57VT=your_ga_session_cookie_here
```

### Generating the Encryption Key

- Use a standard Fernet key (URL‑safe base64 for a 32‑byte key).
- Generate once and keep stable across deployments so encrypted tokens remain readable.

Example (run once and copy the output into `.env`):

```bash
python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
```

Set the resulting string as `API_CONFIG_ENCRYPTION_KEY` (do not base64‑decode it).

## Database Schema

### api_configurations Collection

```javascript
{
  _id: ObjectId,
  service_name: String,        // Unique service identifier
  base_url: String,           // Base API URL
  endpoints: Object,          // Endpoint configurations
  credentials: {
    login_token_encrypted: String,  // Encrypted token
    session_cookies: Object,        // Session cookies
    headers: Object                 // Request headers
  },
  enabled: Boolean,           // Service enabled flag
  last_updated: Date,         // Last modification time
  source: String              // Configuration source
}
```

### api_config_audit Collection

```javascript
{
  _id: ObjectId,
  service_name: String,       // Service that was modified
  source: String,             // Configuration source
  action: String,             // Action performed (save, delete, etc.)
  user_id: String,           // User who made the change
  timestamp: Date,           // When the change occurred
  ip_address: String,        // User's IP address
  user_agent: String         // User's browser/client
}
```

## Performance Considerations

### Caching Strategy

- **In-Memory Cache**: 5-minute TTL for frequently accessed configurations
- **Cache Invalidation**: Manual and automatic cache clearing
- **Lazy Loading**: Configurations loaded on first access
- **Background Refresh**: Periodic cache updates for active services

### Database Optimization

- **Indexes**: Optimized queries on service_name and source fields
- **Connection Pooling**: Efficient database connection management
- **Batch Operations**: Bulk configuration updates when possible
- **Query Optimization**: Minimal database queries through caching

## Monitoring & Alerting

### Health Checks

- **API Connectivity**: Regular connection testing for all configured services
- **Configuration Validation**: Automatic validation of saved configurations
- **Performance Monitoring**: Response time tracking and alerting
- **Error Rate Monitoring**: Failed request tracking and notifications

### Logging

- **Configuration Changes**: All modifications logged with user details
- **API Calls**: External API interactions logged for debugging
- **Error Tracking**: Detailed error logs with stack traces
- **Performance Metrics**: Response times and success rates logged

## Migration Guide

### From Hardcoded Configuration

1. **Identify Current Configuration**: Locate hardcoded API settings
2. **Create Configuration**: Use admin panel to create new configuration
3. **Update Service Code**: Replace hardcoded values with API config service calls
4. **Test Integration**: Verify functionality with new configuration system
5. **Remove Old Code**: Clean up hardcoded configuration values

### Backward Compatibility

- Environment variable support maintained
- Gradual migration path available
- Fallback to default configurations
- No breaking changes to existing functionality

## Best Practices

### Security

- **Encrypt Sensitive Data**: Always encrypt tokens and passwords
- **Rotate Credentials**: Regular credential rotation and updates
- **Audit Access**: Monitor who accesses and modifies configurations
- **Validate Inputs**: Strict validation of all configuration inputs

### Performance

- **Cache Configurations**: Use caching to avoid repeated database queries
- **Batch Updates**: Group configuration changes when possible
- **Monitor Performance**: Track API response times and success rates
- **Optimize Queries**: Use efficient database queries and indexes

### Maintenance

- **Document Changes**: Maintain clear documentation of configuration changes
- **Test Configurations**: Always test configurations before deployment
- **Monitor Health**: Regular health checks and monitoring
- **Plan Rollbacks**: Have rollback procedures for failed configurations

## Troubleshooting

### Common Issues

1. **Configuration Not Found**: Check service name and source priority
2. **Authentication Failures**: Verify credentials and test connection
3. **Timeout Errors**: Adjust timeout settings and retry configuration
4. **Cache Issues**: Clear cache and reload configuration

### Debug Commands

```bash
# Test API configuration system
python test_api_config_system.py

# Check specific service configuration
python -c "
import asyncio
from api_v1.services.api_config import get_api_config_service
async def test():
    service = get_api_config_service()
    config = await service.get_api_config('external_cart')
    print(f'Config: {config}')
asyncio.run(test())
"

# Test API connection
python -c "
import asyncio
from api_v1.services.api_config import get_api_config_service
async def test():
    service = get_api_config_service()
    result = await service.test_api_connection('external_cart')
    print(f'Test result: {result}')
asyncio.run(test())
"
```

## Future Enhancements

### Planned Features

- **Configuration Templates**: Pre-defined templates for common services
- **Bulk Import/Export**: Configuration backup and restore functionality
- **Advanced Monitoring**: Real-time dashboards and alerting
- **API Versioning**: Support for multiple API versions
- **Configuration Validation**: Advanced validation rules and testing

### Integration Opportunities

- **CI/CD Integration**: Automated configuration deployment
- **External Config Sources**: Support for external configuration management
- **Multi-Environment**: Environment-specific configuration management
- **Service Discovery**: Automatic service endpoint discovery
