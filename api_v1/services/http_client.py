"""
Unified HTTP Client Service

Consolidates HTTP request handling patterns from across the codebase
into a single, reusable service with consistent retry logic, error handling,
and logging.
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

import aiohttp
from aiohttp import ClientTimeout, ClientSession

from ..utils.authentication import <PERSON>thenticationHelper
from ..utils.logging import get_api_logger, LogLevel
from ..core.exceptions import HTTPClientError

logger = logging.getLogger(__name__)


class HTTPMethod(str, Enum):
    """HTTP methods"""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"


@dataclass
class HTTPResponse:
    """Standardized HTTP response wrapper"""
    success: bool
    status_code: Optional[int] = None
    data: Optional[Dict[str, Any]] = None
    text: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    error: Optional[str] = None
    execution_time_ms: Optional[int] = None
    retry_count: int = 0


@dataclass
class HTTPRequest:
    """HTTP request configuration"""
    method: HTTPMethod
    url: str
    headers: Optional[Dict[str, str]] = None
    params: Optional[Dict[str, Any]] = None
    json_data: Optional[Dict[str, Any]] = None
    data: Optional[Union[str, bytes]] = None
    cookies: Optional[Dict[str, str]] = None
    timeout: int = 30
    max_retries: int = 3
    retry_delays: Optional[list] = None

    def __post_init__(self):
        if self.retry_delays is None:
            self.retry_delays = [1, 2, 4]
        if self.headers is None:
            self.headers = {}
        if self.cookies is None:
            self.cookies = {}


class UnifiedHTTPClient:
    """
    Unified HTTP client for all API requests.
    
    Consolidates HTTP request patterns from:
    - external_api_service.py
    - api_testing.py
    - api_health_monitor.py
    - card_service.py
    """

    def __init__(self):
        self._session: Optional[ClientSession] = None
        self._auth_helper = AuthenticationHelper()
        # Keep unified client logging concise; only important responses
        self._api_logger = get_api_logger("unified_http_client", LogLevel.INFO)

    async def __aenter__(self):
        """Async context manager entry"""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()

    async def _ensure_session(self):
        """Ensure HTTP session is available"""
        is_closed = False
        if self._session is not None:
            try:
                closed_attr = getattr(self._session, "closed", False)
                is_closed = closed_attr if isinstance(closed_attr, bool) else False
            except Exception:
                is_closed = False
        if self._session is None or is_closed:
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )
            self._session = ClientSession(
                connector=connector,
                timeout=ClientTimeout(total=60),
                headers=self._auth_helper.build_default_headers(),
            )

    async def close(self):
        """Close HTTP session"""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None

    async def request(
        self,
        request_config: HTTPRequest,
        auth_config: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        operation: Optional[str] = None,
    ) -> HTTPResponse:
        """
        Make HTTP request with comprehensive error handling and retry logic
        
        Args:
            request_config: HTTP request configuration
            auth_config: Authentication configuration
            user_id: User ID for logging context
            operation: Operation name for logging context
            
        Returns:
            HTTPResponse with result data
        """
        await self._ensure_session()
        
        start_time = time.time()
        
        # Build headers
        headers = request_config.headers.copy()
        if auth_config:
            auth_headers = self._auth_helper.build_auth_headers(auth_config)
            headers.update(auth_headers)

        # Create logging context
        context = self._api_logger.create_context(
            user_id=user_id, 
            operation=operation or "http_request"
        )

        # Suppress auth context logs to keep output to response body only

        # Perform request with retries
        for attempt in range(request_config.max_retries + 1):
            try:
                # Log the request
                self._api_logger.log_request(
                    context=context,
                    method=request_config.method.value,
                    url=request_config.url,
                    headers=self._auth_helper.mask_sensitive_data(headers),
                    query_params=request_config.params,
                    body=request_config.json_data,
                    timeout=float(request_config.timeout),
                    retry_count=attempt,
                )

                # Suppress verbose request debug logs; response logging is handled centrally

                # Make the request; support both async CM and awaited response for testing
                req = self._session.request(
                    method=request_config.method.value,
                    url=request_config.url,
                    headers=headers,
                    params=request_config.params,
                    json=request_config.json_data,
                    data=request_config.data,
                    cookies=request_config.cookies or None,
                    timeout=ClientTimeout(total=request_config.timeout),
                )

                async def _consume_response(response):
                    execution_time_ms = int((time.time() - start_time) * 1000)
                    response_text = await response.text()
                    response_data = None
                    try:
                        if response_text:
                            response_data = json.loads(response_text)
                    except json.JSONDecodeError:
                        response_data = None

                    success = 200 <= response.status < 400
                    http_response = HTTPResponse(
                        success=success,
                        status_code=response.status,
                        data=response_data,
                        text=response_text,
                        headers=dict(response.headers),
                        execution_time_ms=execution_time_ms,
                        retry_count=attempt,
                    )
                    # Suppress HTTP client response logging to avoid duplicate logs.
                    # External services (e.g., external_api_service) handle response logging.
                    return http_response

                # Determine how to consume the returned object (handles AsyncMock patterns)
                response_obj = None
                try:
                    import asyncio as _asyncio  # local alias to avoid shadowing
                    if _asyncio.iscoroutine(req):
                        req = await req
                    if hasattr(req, "__aenter__") and hasattr(req, "__aexit__"):
                        async with req as response:
                            http_response = await _consume_response(response)
                    else:
                        response_obj = req
                        http_response = await _consume_response(response_obj)
                except TypeError:
                    # Fallback: treat as awaitable returning a response
                    response_obj = await req
                    http_response = await _consume_response(response_obj)

                if http_response.success:
                    return http_response
                else:
                    error_msg = f"HTTP {http_response.status_code}: {http_response.text}"
                    # Reduce noise: rely on centralized response logging
                    logger.debug(f"Request failed: {error_msg}")
                    if attempt == request_config.max_retries:
                        http_response.error = error_msg
                        return http_response

            except asyncio.TimeoutError:
                error_msg = f"Request timeout after {request_config.timeout}s"
                logger.debug(f"Request timeout (attempt {attempt + 1}): {error_msg}")
                
                if attempt == request_config.max_retries:
                    return HTTPResponse(
                        success=False,
                        error=error_msg,
                        execution_time_ms=int((time.time() - start_time) * 1000),
                        retry_count=attempt,
                    )

            except Exception as e:
                error_msg = f"Request failed: {str(e)}"
                logger.debug(f"Request error (attempt {attempt + 1}): {error_msg}")
                
                if attempt == request_config.max_retries:
                    return HTTPResponse(
                        success=False,
                        error=error_msg,
                        execution_time_ms=int((time.time() - start_time) * 1000),
                        retry_count=attempt,
                    )

            # Wait before retry
            if attempt < request_config.max_retries:
                delay = request_config.retry_delays[
                    min(attempt, len(request_config.retry_delays) - 1)
                ]
                logger.debug(f"Waiting {delay}s before retry...")
                await asyncio.sleep(delay)

        # Should not reach here, but return error response as fallback
        return HTTPResponse(
            success=False,
            error="Maximum retries exceeded",
            execution_time_ms=int((time.time() - start_time) * 1000),
            retry_count=request_config.max_retries,
        )

    def _determine_auth_method(
        self, 
        headers: Dict[str, str], 
        auth_config: Optional[Dict[str, Any]]
    ) -> str:
        """Determine authentication method from headers and config"""
        if headers.get("Authorization"):
            auth_header = headers["Authorization"]
            if auth_header.startswith("Bearer "):
                return "bearer_token"
            elif auth_header.startswith("Basic "):
                return "basic_auth"
            else:
                return "custom_auth"
        elif auth_config and auth_config.get("type"):
            return auth_config["type"]
        else:
            return "none"


# Singleton instance
_http_client_instance = None


def get_http_client() -> UnifiedHTTPClient:
    """Get singleton instance of the unified HTTP client"""
    global _http_client_instance
    if _http_client_instance is None:
        _http_client_instance = UnifiedHTTPClient()
    return _http_client_instance
