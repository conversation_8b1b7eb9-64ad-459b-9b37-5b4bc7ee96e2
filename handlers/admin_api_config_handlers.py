"""
Enhanced Admin panel handlers for API configuration management
Provides improved UI, templates, bulk operations, and better organization
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import json

from aiogram import Router, F
from aiogram.types import (
    Message,
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
)
import html as _html
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from api_v1.services.api_config import (
    get_api_config_service,
    APIConfiguration,
    APIEndpoint as APIEndpoint,
    APICredentials,
    ConfigSource,
)
from services.api_config_templates import get_template_service, TemplateCategory
from services.api_config_bulk import get_bulk_service
from services.api_config_validation import get_validation_service, ValidationLevel
from api_v1.utils.error_handling import get_error_handler as get_error_service
from services.auth_profile_service import get_auth_profile_service
from services.user_service import get_user_service
from utils.decorators import admin_required, error_handler
from utils.keyboards import create_pagination_keyboard
from middleware import attach_common_middlewares

logger = logging.getLogger(__name__)

router = Router()
# Apply middleware to the router
attach_common_middlewares(router)


class APIConfigStates(StatesGroup):
    """Enhanced states for API configuration management"""

    # Template-based creation
    WAITING_TEMPLATE_SELECTION = State()
    WAITING_TEMPLATE_CONFIG = State()

    # Manual creation
    WAITING_SERVICE_NAME = State()
    WAITING_BASE_URL = State()
    WAITING_DISPLAY_NAME = State()
    WAITING_DESCRIPTION = State()
    WAITING_CATEGORY = State()

    # Credentials
    WAITING_LOGIN_TOKEN = State()
    WAITING_COOKIE_NAME = State()
    WAITING_COOKIE_VALUE = State()
    WAITING_HEADER_NAME = State()
    WAITING_HEADER_VALUE = State()

    # Endpoints
    WAITING_ENDPOINT_NAME = State()
    WAITING_ENDPOINT_URL = State()
    WAITING_ENDPOINT_METHOD = State()
    WAITING_ENDPOINT_TIMEOUT = State()

    # Bulk operations
    WAITING_IMPORT_DATA = State()
    WAITING_BULK_SELECTION = State()
    WAITING_BULK_ACTION = State()

    # Search and filter
    WAITING_SEARCH_QUERY = State()
    WAITING_FILTER_CATEGORY = State()


async def _show_main_config_menu(
    message_or_callback,
    state: FSMContext,
    is_callback: bool = True,
    category_filter: Optional[str] = None,
    search_query: Optional[str] = None,
):
    """Enhanced main configuration menu with filtering and organization"""
    api_service = get_api_config_service()

    # Get all configurations
    configurations = await api_service.get_all_configurations()

    # Apply filters
    if category_filter:
        configurations = {
            name: config
            for name, config in configurations.items()
            if getattr(config, "category", "general") == category_filter
        }

    if search_query:
        search_lower = search_query.lower()
        configurations = {
            name: config
            for name, config in configurations.items()
            if (
                search_lower in name.lower()
                or search_lower in getattr(config, "display_name", "").lower()
                or search_lower in getattr(config, "description", "").lower()
            )
        }

    # Build header
    header = "🔧 <b>API Configuration Management</b>"
    if category_filter:
        header += f" - Category: {category_filter.title()}"
    if search_query:
        header += f" - Search: {_html.escape(search_query)}"
    header += "\n\n"

    if not configurations:
        no_config_text = header + "No API configurations found."
        if category_filter or search_query:
            no_config_text += "\n\n🔍 Try adjusting your filters or search terms."
        else:
            no_config_text += "\n\nGet started by creating your first configuration!"

        keyboard = [
            [
                InlineKeyboardButton(
                    text="➕ Create from Template", callback_data="api_config_template"
                )
            ],
            [
                InlineKeyboardButton(
                    text="➕ Create Manually", callback_data="api_config_add_manual"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📥 Import Configurations", callback_data="api_config_import"
                )
            ],
        ]

        if category_filter or search_query:
            keyboard.append(
                [
                    InlineKeyboardButton(
                        text="🔄 Clear Filters", callback_data="api_config_main"
                    )
                ]
            )

        reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard)

        if is_callback:
            await message_or_callback.message.edit_text(
                no_config_text, parse_mode="HTML", reply_markup=reply_markup
            )
        else:
            await message_or_callback.answer(
                no_config_text, parse_mode="HTML", reply_markup=reply_markup
            )
        return

    # Group configurations by category
    categories = {}
    for service_name, config in configurations.items():
        cat = getattr(config, "category", "general")
        if cat not in categories:
            categories[cat] = []
        categories[cat].append((service_name, config))

    # Build configuration list
    config_text = header
    keyboard = []

    # Quick stats
    total_configs = len(configurations)
    enabled_configs = sum(1 for config in configurations.values() if config.enabled)
    config_text += f"📊 <b>Summary:</b> {enabled_configs}/{total_configs} enabled, {len(categories)} categories\n\n"

    # Show configurations by category
    for category, configs in sorted(categories.items()):
        category_icon = {
            "ecommerce": "🛒",
            "payment": "💳",
            "notification": "📧",
            "analytics": "📈",
            "authentication": "🔐",
            "general": "⚙️",
        }.get(category, "📁")

        config_text += f"{category_icon} <b>{category.title()}</b>\n"

        for service_name, config in sorted(configs):
            status = "🟢" if config.enabled else "🔴"
            display_name = getattr(config, "display_name", service_name)

            config_text += f"  {status} {_html.escape(display_name)}\n"

            keyboard.append(
                [
                    InlineKeyboardButton(
                        text=f"⚙️ {display_name}",
                        callback_data=f"api_config_view:{service_name}",
                    )
                ]
            )

        config_text += "\n"

    # Action buttons
    action_buttons = [
        [
            InlineKeyboardButton(
                text="➕ Create from Template", callback_data="api_config_template"
            ),
            InlineKeyboardButton(
                text="➕ Manual Create", callback_data="api_config_add_manual"
            ),
        ],
        [
            InlineKeyboardButton(
                text="🔐 Auth Profiles", callback_data="auth_profiles_main"
            ),
            InlineKeyboardButton(
                text="🔗 Profile Assignment", callback_data="api_config_profile_assign"
            ),
        ],
        [
            InlineKeyboardButton(text="📥 Import", callback_data="api_config_import"),
            InlineKeyboardButton(text="📤 Export", callback_data="api_config_export"),
        ],
        [
            InlineKeyboardButton(text="🔍 Search", callback_data="api_config_search"),
            InlineKeyboardButton(
                text="📂 Filter by Category", callback_data="api_config_filter"
            ),
        ],
        [
            InlineKeyboardButton(
                text="🔧 Bulk Actions", callback_data="api_config_bulk"
            ),
            InlineKeyboardButton(
                text="📋 Audit Trail", callback_data="api_config_audit"
            ),
        ],
    ]

    if category_filter or search_query:
        action_buttons.append(
            [
                InlineKeyboardButton(
                    text="🔄 Clear Filters", callback_data="api_config_main"
                )
            ]
        )

    keyboard.extend(action_buttons)

    reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard)

    if is_callback:
        await message_or_callback.message.edit_text(
            config_text, parse_mode="HTML", reply_markup=reply_markup
        )
    else:
        await message_or_callback.answer(
            config_text, parse_mode="HTML", reply_markup=reply_markup
        )


@router.message(Command("api_config"))
@admin_required
@error_handler
async def cmd_api_config(message: Message, state: FSMContext):
    """Enhanced API configuration management command"""
    await _show_main_config_menu(message, state, is_callback=False)


# Template-based configuration creation
@router.callback_query(F.data == "api_config_template")
@admin_required
@error_handler
async def callback_api_config_template(callback: CallbackQuery, state: FSMContext):
    """Show template selection for creating new API configuration"""
    template_service = get_template_service()
    templates = template_service.list_templates()

    if not templates:
        await callback.answer("❌ No templates available", show_alert=True)
        return

    # Group templates by category
    categories = {}
    for template in templates:
        if template.category not in categories:
            categories[template.category] = []
        categories[template.category].append(template)

    text = "🎯 <b>Choose a Template</b>\n\n"
    text += "Select a pre-configured template to quickly set up your API:\n\n"

    keyboard = []

    for category, category_templates in categories.items():
        category_icon = {
            TemplateCategory.ECOMMERCE: "🛒",
            TemplateCategory.PAYMENT: "💳",
            TemplateCategory.NOTIFICATION: "📧",
            TemplateCategory.ANALYTICS: "📈",
            TemplateCategory.AUTHENTICATION: "🔐",
            TemplateCategory.GENERAL: "⚙️",
        }.get(category, "📁")

        text += f"{category_icon} <b>{category.value.title()}</b>\n"

        for template in category_templates:
            text += f"  {template.icon} {template.name}\n"
            keyboard.append(
                [
                    InlineKeyboardButton(
                        text=f"{template.icon} {template.name}",
                        callback_data=f"api_template_select:{template.id}",
                    )
                ]
            )
        text += "\n"

    keyboard.append(
        [InlineKeyboardButton(text="⬅️ Back", callback_data="api_config_main")]
    )

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data.startswith("api_template_select:"))
@admin_required
@error_handler
async def callback_template_select(callback: CallbackQuery, state: FSMContext):
    """Handle template selection and show configuration form"""
    template_id = callback.data.split(":", 1)[1]
    template_service = get_template_service()
    template = template_service.get_template(template_id)

    if not template:
        await callback.answer("❌ Template not found", show_alert=True)
        return

    # Store template info in state
    await state.update_data(template_id=template_id)
    await state.set_state(APIConfigStates.WAITING_TEMPLATE_CONFIG)

    text = f"🎯 <b>Configure {template.name}</b>\n\n"
    text += f"📝 <b>Description:</b> {template.description}\n\n"

    if template.setup_instructions:
        text += f"📋 <b>Setup Instructions:</b>\n{template.setup_instructions}\n\n"

    text += "📋 <b>Required Information:</b>\n"
    for field in template.required_fields:
        field_name = field.replace("_", " ").title()
        text += f"• {field_name}\n"

    if template.optional_fields:
        text += f"\n🔧 <b>Optional Fields:</b>\n"
        for field in template.optional_fields:
            field_name = field.replace("_", " ").title()
            text += f"• {field_name}\n"

    text += "\n💡 <b>Next Steps:</b>\n"
    text += "1. Enter service name\n"
    text += "2. Provide API base URL\n"
    text += "3. Configure authentication\n"
    text += "4. Test and save\n\n"
    text += "Please enter a unique service name for this configuration:"

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="api_config_template")],
        [
            InlineKeyboardButton(
                text="📖 View Documentation",
                callback_data=f"api_template_docs:{template_id}",
            )
        ],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data == "api_config_refresh")
@admin_required
@error_handler
async def callback_api_config_refresh(callback: CallbackQuery, state: FSMContext):
    """Refresh API configurations and show main menu"""
    api_service = get_api_config_service()
    await api_service.invalidate_cache()

    await callback.answer("🔄 Configuration cache refreshed!")
    await _show_main_config_menu(callback, state)


# Search and filter functionality
@router.callback_query(F.data == "api_config_search")
@admin_required
@error_handler
async def callback_api_config_search(callback: CallbackQuery, state: FSMContext):
    """Start search for API configurations"""
    await state.set_state(APIConfigStates.WAITING_SEARCH_QUERY)

    text = "🔍 <b>Search API Configurations</b>\n\n"
    text += "Enter search terms to find configurations by:\n"
    text += "• Service name\n"
    text += "• Display name\n"
    text += "• Description\n"
    text += "• Tags\n\n"
    text += "Type your search query:"

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="api_config_main")]
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.message(APIConfigStates.WAITING_SEARCH_QUERY)
@admin_required
@error_handler
async def message_search_query(message: Message, state: FSMContext):
    """Handle search query input"""
    search_query = message.text.strip()

    if not search_query:
        await message.answer("❌ Please enter a search query.")
        return

    await state.clear()
    await _show_main_config_menu(
        message, state, is_callback=False, search_query=search_query
    )


@router.callback_query(F.data == "api_config_filter")
@admin_required
@error_handler
async def callback_api_config_filter(callback: CallbackQuery, state: FSMContext):
    """Show category filter options"""
    text = "📂 <b>Filter by Category</b>\n\n"
    text += "Choose a category to filter configurations:\n\n"

    categories = [
        ("🛒", "ecommerce", "E-commerce & Shopping"),
        ("💳", "payment", "Payment & Billing"),
        ("📧", "notification", "Notifications & Messaging"),
        ("📈", "analytics", "Analytics & Tracking"),
        ("🔐", "authentication", "Authentication & Security"),
        ("⚙️", "general", "General & Utilities"),
    ]

    keyboard = []
    for icon, category, description in categories:
        text += f"{icon} <b>{description}</b>\n"
        keyboard.append(
            [
                InlineKeyboardButton(
                    text=f"{icon} {description}",
                    callback_data=f"api_filter_category:{category}",
                )
            ]
        )

    keyboard.append(
        [InlineKeyboardButton(text="⬅️ Back", callback_data="api_config_main")]
    )

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data.startswith("api_filter_category:"))
@admin_required
@error_handler
async def callback_filter_category(callback: CallbackQuery, state: FSMContext):
    """Apply category filter"""
    category = callback.data.split(":", 1)[1]
    await _show_main_config_menu(callback, state, category_filter=category)


# Bulk operations
@router.callback_query(F.data == "api_config_bulk")
@admin_required
@error_handler
async def callback_api_config_bulk(callback: CallbackQuery, state: FSMContext):
    """Show bulk operations menu"""
    api_service = get_api_config_service()
    configurations = await api_service.get_all_configurations()

    text = "🔧 <b>Bulk Operations</b>\n\n"
    text += f"📊 Total configurations: {len(configurations)}\n\n"
    text += "Choose a bulk operation:\n\n"

    keyboard = [
        [
            InlineKeyboardButton(
                text="✅ Bulk Enable", callback_data="api_bulk_enable"
            ),
            InlineKeyboardButton(
                text="❌ Bulk Disable", callback_data="api_bulk_disable"
            ),
        ],
        [
            InlineKeyboardButton(
                text="📤 Export All", callback_data="api_bulk_export_all"
            ),
            InlineKeyboardButton(text="📥 Import", callback_data="api_config_import"),
        ],
        [
            InlineKeyboardButton(text="🧪 Test All", callback_data="api_bulk_test"),
            InlineKeyboardButton(
                text="🔄 Refresh All", callback_data="api_bulk_refresh"
            ),
        ],
        [InlineKeyboardButton(text="⬅️ Back", callback_data="api_config_main")],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data == "api_config_import")
@admin_required
@error_handler
async def callback_api_config_import(callback: CallbackQuery, state: FSMContext):
    """Start import process"""
    await state.set_state(APIConfigStates.WAITING_IMPORT_DATA)

    text = "📥 <b>Import API Configurations</b>\n\n"
    text += "Paste your configuration data in JSON or YAML format.\n\n"
    text += "📋 <b>Supported formats:</b>\n"
    text += "• JSON export from this system\n"
    text += "• YAML configuration files\n"
    text += "• Postman collections (basic support)\n\n"
    text += "⚠️ <b>Important:</b>\n"
    text += "• Existing configurations will be preserved\n"
    text += "• Use 'overwrite' in the data to replace existing ones\n"
    text += "• All imports are validated before saving\n\n"
    text += "Paste your configuration data:"

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="api_config_bulk")]
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.message(APIConfigStates.WAITING_IMPORT_DATA)
@admin_required
@error_handler
async def message_import_data(message: Message, state: FSMContext):
    """Handle import data input"""
    import_data = message.text.strip()

    if not import_data:
        await message.answer("❌ Please provide configuration data to import.")
        return

    bulk_service = get_bulk_service()
    user_id = str(message.from_user.id)

    # Try JSON first, then YAML
    try:
        result = await bulk_service.import_configurations(
            import_data, format="json", overwrite_existing=False, user_id=user_id
        )
    except:
        try:
            result = await bulk_service.import_configurations(
                import_data, format="yaml", overwrite_existing=False, user_id=user_id
            )
        except Exception as e:
            await message.answer(f"❌ Import failed: {str(e)}")
            await state.clear()
            return

    # Show results
    text = "📥 <b>Import Results</b>\n\n"
    text += f"📊 {result.get_summary()}\n\n"

    if result.success_count > 0:
        text += f"✅ <b>Successfully imported:</b>\n"
        for item in result.processed_items:
            text += f"• {item}\n"
        text += "\n"

    if result.errors:
        text += f"❌ <b>Errors:</b>\n"
        for error in result.errors[:5]:  # Show first 5 errors
            text += f"• {error['item']}: {error['error']}\n"
        if len(result.errors) > 5:
            text += f"• ... and {len(result.errors) - 5} more errors\n"
        text += "\n"

    if result.warnings:
        text += f"⚠️ <b>Warnings:</b>\n"
        for warning in result.warnings[:3]:  # Show first 3 warnings
            text += f"• {warning}\n"
        if len(result.warnings) > 3:
            text += f"• ... and {len(result.warnings) - 3} more warnings\n"

    keyboard = [[InlineKeyboardButton(text="✅ Done", callback_data="api_config_main")]]

    await message.answer(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )
    await state.clear()


@router.callback_query(F.data == "api_bulk_export_all")
@admin_required
@error_handler
async def callback_bulk_export_all(callback: CallbackQuery, state: FSMContext):
    """Export all configurations"""
    bulk_service = get_bulk_service()

    try:
        export_data = await bulk_service.export_configurations(
            service_names=None, format="json", include_credentials=False
        )

        # Send as file if too long, otherwise as message
        if len(export_data) > 3000:
            await callback.answer("📤 Preparing export file...")
            # In a real implementation, you'd create and send a file
            await callback.message.answer(
                "📤 <b>Export Complete</b>\n\n"
                "⚠️ Export data is too large to display here.\n"
                "In a production system, this would be sent as a downloadable file.\n\n"
                f"📊 Export size: {len(export_data)} characters",
                parse_mode="HTML",
            )
        else:
            text = "📤 <b>Exported Configurations</b>\n\n"
            text += "```json\n" + export_data + "\n```"

            await callback.message.edit_text(
                text,
                parse_mode="Markdown",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="✅ Done", callback_data="api_config_main"
                            )
                        ]
                    ]
                ),
            )
    except Exception as e:
        await callback.answer(f"❌ Export failed: {str(e)}", show_alert=True)


# Enhanced validation and testing
@router.callback_query(F.data.startswith("api_config_validate:"))
@admin_required
@error_handler
async def callback_validate_config(callback: CallbackQuery, state: FSMContext):
    """Validate a specific API configuration"""
    service_name = callback.data.split(":", 1)[1]
    api_service = get_api_config_service()
    validation_service = get_validation_service()

    config = await api_service.get_api_config(service_name)
    if not config:
        await callback.answer("❌ Configuration not found", show_alert=True)
        return

    # Perform validation
    validation_results = await validation_service.validate_configuration(config)

    text = f"🔍 <b>Validation Results: {_html.escape(config.display_name or service_name)}</b>\n\n"

    if not validation_results:
        text += "✅ <b>All checks passed!</b>\n"
        text += "Your configuration looks good and ready to use.\n\n"
    else:
        # Group by severity
        errors = [r for r in validation_results if r.level == ValidationLevel.ERROR]
        warnings = [r for r in validation_results if r.level == ValidationLevel.WARNING]
        info = [r for r in validation_results if r.level == ValidationLevel.INFO]

        if errors:
            text += f"❌ <b>Errors ({len(errors)}):</b>\n"
            for error in errors[:3]:  # Show first 3 errors
                text += f"• <b>{error.field}:</b> {error.message}\n"
                if error.suggestion:
                    text += f"  💡 {error.suggestion}\n"
            if len(errors) > 3:
                text += f"• ... and {len(errors) - 3} more errors\n"
            text += "\n"

        if warnings:
            text += f"⚠️ <b>Warnings ({len(warnings)}):</b>\n"
            for warning in warnings[:3]:  # Show first 3 warnings
                text += f"• <b>{warning.field}:</b> {warning.message}\n"
                if warning.suggestion:
                    text += f"  💡 {warning.suggestion}\n"
            if len(warnings) > 3:
                text += f"• ... and {len(warnings) - 3} more warnings\n"
            text += "\n"

        if info:
            text += f"ℹ️ <b>Suggestions ({len(info)}):</b>\n"
            for suggestion in info[:2]:  # Show first 2 suggestions
                text += f"• <b>{suggestion.field}:</b> {suggestion.message}\n"
            if len(info) > 2:
                text += f"• ... and {len(info) - 2} more suggestions\n"

    keyboard = [
        [
            InlineKeyboardButton(
                text="🧪 Test Connection",
                callback_data=f"api_config_test:{service_name}",
            ),
            InlineKeyboardButton(
                text="✏️ Edit Config", callback_data=f"api_config_edit:{service_name}"
            ),
        ],
        [
            InlineKeyboardButton(
                text="⬅️ Back", callback_data=f"api_config_view:{service_name}"
            )
        ],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data.startswith("api_config_test:"))
@admin_required
@error_handler
async def callback_test_config(callback: CallbackQuery, state: FSMContext):
    """Test API configuration connection"""
    service_name = callback.data.split(":", 1)[1]
    api_service = get_api_config_service()
    validation_service = get_validation_service()

    config = await api_service.get_api_config(service_name)
    if not config:
        await callback.answer("❌ Configuration not found", show_alert=True)
        return

    await callback.answer("🧪 Testing connection...")

    # Perform connection test
    test_result = await validation_service.test_api_connection(config)

    text = f"🧪 <b>Connection Test: {_html.escape(config.display_name or service_name)}</b>\n\n"

    if test_result.success:
        text += "✅ <b>Connection Successful!</b>\n"
        if test_result.status_code:
            text += f"📊 Status Code: {test_result.status_code}\n"
        if test_result.response_time_ms:
            text += f"⏱️ Response Time: {test_result.response_time_ms}ms\n"
        text += "\n"

        # Show endpoint results if available
        if test_result.endpoint_results:
            successful_endpoints = sum(
                1
                for r in test_result.endpoint_results.values()
                if r.get("success", False)
            )
            total_endpoints = len(test_result.endpoint_results)
            text += f"📡 <b>Endpoints:</b> {successful_endpoints}/{total_endpoints} working\n\n"

            for endpoint_name, result in list(test_result.endpoint_results.items())[:3]:
                status = "✅" if result.get("success", False) else "❌"
                text += f"{status} {endpoint_name}\n"
    else:
        text += "❌ <b>Connection Failed</b>\n"
        if test_result.error_message:
            text += f"🔍 Error: {test_result.error_message}\n"
        text += "\n"

    # Show suggestions
    if test_result.suggestions:
        text += "💡 <b>Suggestions:</b>\n"
        for suggestion in test_result.suggestions[:3]:
            text += f"• {suggestion}\n"

    keyboard = [
        [
            InlineKeyboardButton(
                text="🔍 Validate Config",
                callback_data=f"api_config_validate:{service_name}",
            ),
            InlineKeyboardButton(
                text="✏️ Edit Config", callback_data=f"api_config_edit:{service_name}"
            ),
        ],
        [
            InlineKeyboardButton(
                text="⬅️ Back", callback_data=f"api_config_view:{service_name}"
            )
        ],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


# Help and documentation
@router.callback_query(F.data.startswith("api_template_docs:"))
@admin_required
@error_handler
async def callback_template_docs(callback: CallbackQuery, state: FSMContext):
    """Show template documentation"""
    template_id = callback.data.split(":", 1)[1]
    template_service = get_template_service()
    template = template_service.get_template(template_id)

    if not template:
        await callback.answer("❌ Template not found", show_alert=True)
        return

    text = f"📖 <b>Documentation: {template.name}</b>\n\n"
    text += f"📝 <b>Description:</b>\n{template.description}\n\n"

    if template.setup_instructions:
        text += f"📋 <b>Setup Instructions:</b>\n{template.setup_instructions}\n\n"

    text += f"🏷️ <b>Category:</b> {template.category.value.title()}\n"
    text += f"🔖 <b>Tags:</b> {', '.join(template.tags)}\n\n"

    text += f"📋 <b>Required Fields:</b>\n"
    for field in template.required_fields:
        field_name = field.replace("_", " ").title()
        text += f"• {field_name}\n"

    if template.optional_fields:
        text += f"\n🔧 <b>Optional Fields:</b>\n"
        for field in template.optional_fields:
            field_name = field.replace("_", " ").title()
            text += f"• {field_name}\n"

    text += f"\n📡 <b>Endpoints Included:</b>\n"
    for name, endpoint in template.endpoints.items():
        text += f"• {endpoint.method} {name}: {endpoint.url}\n"

    if template.documentation_url:
        text += f"\n🔗 <b>External Documentation:</b>\n{template.documentation_url}"

    keyboard = [
        [
            InlineKeyboardButton(
                text="✅ Use Template",
                callback_data=f"api_template_select:{template_id}",
            )
        ],
        [InlineKeyboardButton(text="⬅️ Back", callback_data="api_config_template")],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data == "api_config_help")
@admin_required
@error_handler
async def callback_api_config_help(callback: CallbackQuery, state: FSMContext):
    """Show API configuration help and documentation"""
    text = "📚 <b>API Configuration Help</b>\n\n"
    text += "Welcome to the enhanced API Configuration Management system!\n\n"

    text += "🎯 <b>Quick Start:</b>\n"
    text += "1. Use templates for common APIs (recommended)\n"
    text += "2. Or create configurations manually\n"
    text += "3. Test connections before going live\n"
    text += "4. Use bulk operations for efficiency\n\n"

    text += "🔧 <b>Key Features:</b>\n"
    text += "• 📋 Pre-built templates for common services\n"
    text += "• 🔍 Real-time validation and testing\n"
    text += "• 📂 Organization by categories and tags\n"
    text += "• 📥📤 Import/export configurations\n"
    text += "• 🔧 Bulk operations for multiple configs\n"
    text += "• 🔒 Secure credential encryption\n\n"

    text += "💡 <b>Best Practices:</b>\n"
    text += "• Always test configurations after creation\n"
    text += "• Use descriptive names and descriptions\n"
    text += "• Organize with appropriate categories\n"
    text += "• Keep credentials secure and up-to-date\n"
    text += "• Export configurations for backup\n\n"

    text += "🆘 <b>Need Help?</b>\n"
    text += "• Use validation to check for issues\n"
    text += "• Test connections to verify setup\n"
    text += "• Check template documentation\n"
    text += "• Review audit trail for changes"

    keyboard = [
        [
            InlineKeyboardButton(
                text="🎯 Templates", callback_data="api_config_template"
            ),
            InlineKeyboardButton(
                text="📋 Examples", callback_data="api_config_examples"
            ),
        ],
        [
            InlineKeyboardButton(text="🔧 Bulk Ops", callback_data="api_config_bulk"),
            InlineKeyboardButton(text="📋 Audit", callback_data="api_config_audit"),
        ],
        [InlineKeyboardButton(text="⬅️ Back", callback_data="api_config_main")],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


# Template configuration workflow
@router.message(APIConfigStates.WAITING_TEMPLATE_CONFIG)
@admin_required
@error_handler
async def message_template_config(message: Message, state: FSMContext):
    """Handle template-based configuration input"""
    service_name = message.text.strip().lower()
    error_service = get_error_service()

    # Validate service name
    if not service_name or not service_name.replace("_", "").isalnum():
        error_info = error_service.get_error_info("INVALID_SERVICE_NAME")
        error_msg = error_service.format_error_message(error_info)

        await message.answer(
            error_msg,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel", callback_data="api_config_template"
                        )
                    ]
                ]
            ),
        )
        return

    # Check for duplicates
    api_service = get_api_config_service()
    existing_config = await api_service.get_api_config(service_name)

    if existing_config and existing_config.source in [
        ConfigSource.ADMIN_PANEL,
        ConfigSource.DATABASE,
    ]:
        error_info = error_service.get_error_info("DUPLICATE_SERVICE_NAME")
        error_msg = error_service.format_error_message(error_info)

        await message.answer(
            error_msg,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="✏️ Edit Existing",
                            callback_data=f"api_config_edit:{service_name}",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔄 Try Different Name",
                            callback_data="api_config_template",
                        )
                    ],
                ]
            ),
        )
        return

    # Get template data
    data = await state.get_data()
    template_id = data.get("template_id")

    if not template_id:
        await message.answer("❌ Template information lost. Please start over.")
        await state.clear()
        return

    template_service = get_template_service()
    template = template_service.get_template(template_id)

    if not template:
        await message.answer("❌ Template not found. Please start over.")
        await state.clear()
        return

    # Store service name and ask for base URL
    await state.update_data(service_name=service_name, template_step="base_url")

    text = f"✅ Service name: `{service_name}`\n\n"
    text += f"🎯 Using template: {template.name}\n\n"
    text += f"📝 Next, enter the base API URL:\n"
    text += f"Example: {template.base_url_placeholder}\n\n"
    text += "💡 This should be the root URL of your API without any specific endpoints."

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="api_config_template")],
        [
            InlineKeyboardButton(
                text="📖 View Template Docs",
                callback_data=f"api_template_docs:{template_id}",
            )
        ],
    ]

    await message.answer(
        text,
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


# Enhanced error handling for configuration operations
async def _handle_config_error(
    message_or_callback, error: Exception, operation: str = "operation"
):
    """Handle configuration errors with detailed feedback and recovery options"""
    error_service = get_error_service()
    error_info = error_service.categorize_error(error)
    error_msg = error_service.format_error_message(error_info)

    # Get recovery actions
    recovery_actions = error_service.get_recovery_actions(error_info)

    # Build keyboard with recovery options
    keyboard = []
    for action in recovery_actions[:4]:  # Show first 4 actions
        keyboard.append(
            [
                InlineKeyboardButton(
                    text=action["label"],
                    callback_data=f"api_error_action:{action['action']}",
                )
            ]
        )

    # Send error message
    if hasattr(message_or_callback, "message"):  # CallbackQuery
        await message_or_callback.message.edit_text(
            error_msg,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
        )
    else:  # Message
        await message_or_callback.answer(
            error_msg,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
        )


@router.callback_query(F.data.startswith("api_error_action:"))
@admin_required
@error_handler
async def callback_error_action(callback: CallbackQuery, state: FSMContext):
    """Handle error recovery actions"""
    action = callback.data.split(":", 1)[1]

    if action == "test_connection":
        await callback.answer("🧪 Starting connection test...")
        # Redirect to connection test
        await callback_api_config_bulk(callback, state)
    elif action == "edit_config":
        await callback.answer("✏️ Opening configuration editor...")
        await _show_main_config_menu(callback, state)
    elif action == "validate_config":
        await callback.answer("🔍 Running validation...")
        await _show_main_config_menu(callback, state)
    elif action == "use_template":
        await callback.answer("🎯 Opening templates...")
        await callback_api_config_template(callback, state)
    elif action == "get_help":
        await callback_api_config_help(callback, state)
    else:  # back_to_main
        await _show_main_config_menu(callback, state)


@router.callback_query(F.data == "api_config_main")
@admin_required
@error_handler
async def callback_api_config_main(callback: CallbackQuery, state: FSMContext):
    """Show main API configuration menu"""
    await _show_main_config_menu(callback, state)


# Missing edit handlers - Add these to fix the API configuration editing issues


@router.callback_query(F.data.startswith("api_config_edit:"))
@admin_required
@error_handler
async def callback_api_config_edit(callback: CallbackQuery, state: FSMContext):
    """Show edit menu for a specific API configuration"""
    service_name = callback.data.split(":", 1)[1]
    api_service = get_api_config_service()

    config = await api_service.get_api_config(service_name)
    if not config:
        await callback.answer("❌ Configuration not found", show_alert=True)
        return

    # Status indicator
    status_icon = "🟢" if config.enabled else "🔴"
    source_icons = {
        ConfigSource.ADMIN_PANEL: "👤",
        ConfigSource.DATABASE: "💾",
        ConfigSource.ENVIRONMENT: "🌍",
        ConfigSource.DEFAULT: "⚙️",
    }
    source_icon = source_icons.get(config.source, "❓")

    text = f"✏️ <b>Edit Configuration: {_html.escape(config.display_name or service_name)}</b>\n\n"
    text += f"{status_icon} Status: {'Active' if config.enabled else 'Inactive'}\n"
    text += f"{source_icon} Source: {config.source.value.title()}\n"
    text += f"🌐 Base URL: {_html.escape(config.base_url)}\n\n"
    text += "What would you like to edit?"

    keyboard = [
        [
            InlineKeyboardButton(
                text="🌐 Base URL", callback_data=f"api_config_edit_url:{service_name}"
            ),
            InlineKeyboardButton(
                text="🔐 Credentials",
                callback_data=f"api_config_edit_creds:{service_name}",
            ),
        ],
        [
            InlineKeyboardButton(
                text="🔗 Endpoints",
                callback_data=f"api_config_edit_endpoints:{service_name}",
            ),
            InlineKeyboardButton(
                text="⚙️ Settings",
                callback_data=f"api_config_edit_settings:{service_name}",
            ),
        ],
        [
            InlineKeyboardButton(
                text="🧪 Test Config", callback_data=f"api_config_test:{service_name}"
            ),
            InlineKeyboardButton(
                text="🔍 View Details", callback_data=f"api_config_view:{service_name}"
            ),
        ],
        [InlineKeyboardButton(text="⬅️ Back", callback_data="api_config_main")],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data.startswith("api_config_edit_url:"))
@admin_required
@error_handler
async def callback_api_config_edit_url(callback: CallbackQuery, state: FSMContext):
    """Edit base URL for API configuration"""
    service_name = callback.data.split(":", 1)[1]
    api_service = get_api_config_service()

    config = await api_service.get_api_config(service_name)
    if not config:
        await callback.answer("❌ Configuration not found", show_alert=True)
        return

    text = f"🌐 <b>Edit Base URL</b>\n\n"
    text += f"Configuration: {_html.escape(config.display_name or service_name)}\n"
    text += f"Current URL: {_html.escape(config.base_url)}\n\n"
    text += "Enter the new base URL:"

    keyboard = [
        [
            InlineKeyboardButton(
                text="❌ Cancel", callback_data=f"api_config_edit:{service_name}"
            )
        ]
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )

    await state.set_state(APIConfigStates.WAITING_BASE_URL)
    await state.update_data(service_name=service_name, edit_mode=True)


@router.callback_query(F.data.startswith("api_config_edit_creds:"))
@admin_required
@error_handler
async def callback_api_config_edit_creds(callback: CallbackQuery, state: FSMContext):
    """Edit credentials for API configuration"""
    service_name = callback.data.split(":", 1)[1]
    api_service = get_api_config_service()

    config = await api_service.get_api_config(service_name)
    if not config:
        await callback.answer("❌ Configuration not found", show_alert=True)
        return

    text = f"🔐 <b>Edit Credentials</b>\n\n"
    text += f"Configuration: {_html.escape(config.display_name or service_name)}\n\n"

    # Show current credentials (masked)
    if config.credentials and config.credentials.login_token:
        masked_token = (
            config.credentials.login_token[:8] + "..."
            if len(config.credentials.login_token) > 8
            else "***"
        )
        text += f"Current Token: {masked_token}\n"
    else:
        text += "Current Token: Not set\n"

    if config.credentials and config.credentials.session_cookies:
        text += (
            f"Session Cookies: {len(config.credentials.session_cookies)} configured\n"
        )
    else:
        text += "Session Cookies: None\n"

    text += "\nWhat would you like to edit?"

    keyboard = [
        [
            InlineKeyboardButton(
                text="🔑 Login Token",
                callback_data=f"api_config_edit_token:{service_name}",
            ),
            InlineKeyboardButton(
                text="🍪 Cookies",
                callback_data=f"api_config_edit_cookies:{service_name}",
            ),
        ],
        [
            InlineKeyboardButton(
                text="📋 Headers",
                callback_data=f"api_config_edit_headers:{service_name}",
            ),
        ],
        [
            InlineKeyboardButton(
                text="⬅️ Back", callback_data=f"api_config_edit:{service_name}"
            )
        ],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data.startswith("api_config_edit_token:"))
@admin_required
@error_handler
async def callback_api_config_edit_token(callback: CallbackQuery, state: FSMContext):
    """Edit login token for API configuration"""
    service_name = callback.data.split(":", 1)[1]

    text = f"🔑 <b>Edit Login Token</b>\n\n"
    text += f"Configuration: {service_name}\n\n"
    text += "Enter the new login token (JWT or API key):\n\n"
    text += "⚠️ <b>Security Note:</b> The token will be encrypted and stored securely."

    keyboard = [
        [
            InlineKeyboardButton(
                text="❌ Cancel", callback_data=f"api_config_edit_creds:{service_name}"
            )
        ]
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )

    await state.set_state(APIConfigStates.WAITING_LOGIN_TOKEN)
    await state.update_data(service_name=service_name, edit_mode=True)


# Message handlers for edit operations


@router.message(APIConfigStates.WAITING_BASE_URL)
@admin_required
@error_handler
async def message_edit_base_url(message: Message, state: FSMContext):
    """Handle base URL input for editing"""
    data = await state.get_data()
    service_name = data.get("service_name")
    edit_mode = data.get("edit_mode", False)

    if not service_name:
        await message.answer("❌ Session expired. Please start over.")
        await state.clear()
        return

    base_url = message.text.strip()

    # Basic URL validation
    if not base_url.startswith(("http://", "https://")):
        await message.answer(
            "❌ Please enter a valid URL starting with http:// or https://"
        )
        return

    try:
        api_service = get_api_config_service()

        if edit_mode:
            # Update existing configuration
            config = await api_service.get_api_config(service_name)
            if not config:
                await message.answer("❌ Configuration not found")
                await state.clear()
                return

            # Update the base URL
            config.base_url = base_url
            success = await api_service.save_api_config(
                config,
                source=ConfigSource.ADMIN_PANEL,
                user_id=str(message.from_user.id),
            )

            if success:
                await message.answer(
                    f"✅ Base URL updated successfully!\n\n"
                    f"Service: {service_name}\n"
                    f"New URL: {base_url}",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="✏️ Continue Editing",
                                    callback_data=f"api_config_edit:{service_name}",
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🧪 Test Configuration",
                                    callback_data=f"api_config_test:{service_name}",
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="⬅️ Back to Main",
                                    callback_data="api_config_main",
                                )
                            ],
                        ]
                    ),
                )
            else:
                await message.answer("❌ Failed to update base URL. Please try again.")
        else:
            # Continue with template creation flow
            await state.update_data(base_url=base_url, template_step="login_token")

            text = f"✅ Base URL: `{base_url}`\n\n"
            text += "🔑 Next, enter your login token (JWT or API key):\n\n"
            text += "⚠️ This will be encrypted and stored securely."

            keyboard = [
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data="api_config_template"
                    )
                ]
            ]

            await message.answer(
                text,
                parse_mode="Markdown",
                reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
            )
            await state.set_state(APIConfigStates.WAITING_LOGIN_TOKEN)

    except Exception as e:
        logger.error(f"Error updating base URL: {e}")
        await message.answer("❌ An error occurred. Please try again.")

    if edit_mode:
        await state.clear()


@router.message(APIConfigStates.WAITING_LOGIN_TOKEN)
@admin_required
@error_handler
async def message_edit_login_token(message: Message, state: FSMContext):
    """Handle login token input for editing"""
    data = await state.get_data()
    service_name = data.get("service_name")
    edit_mode = data.get("edit_mode", False)

    if not service_name:
        await message.answer("❌ Session expired. Please start over.")
        await state.clear()
        return

    login_token = message.text.strip()

    if not login_token:
        await message.answer("❌ Login token cannot be empty.")
        return

    try:
        api_service = get_api_config_service()

        if edit_mode:
            # Update existing configuration
            config = await api_service.get_api_config(service_name)
            if not config:
                await message.answer("❌ Configuration not found")
                await state.clear()
                return

            # Update credentials
            if not config.credentials:
                config.credentials = APICredentials()

            config.credentials.login_token = login_token

            success = await api_service.save_api_config(
                config,
                source=ConfigSource.ADMIN_PANEL,
                user_id=str(message.from_user.id),
            )

            if success:
                masked_token = (
                    login_token[:8] + "..." if len(login_token) > 8 else "***"
                )
                await message.answer(
                    f"✅ Login token updated successfully!\n\n"
                    f"Service: {service_name}\n"
                    f"Token: {masked_token}",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔐 Edit More Credentials",
                                    callback_data=f"api_config_edit_creds:{service_name}",
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🧪 Test Configuration",
                                    callback_data=f"api_config_test:{service_name}",
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="⬅️ Back to Main",
                                    callback_data="api_config_main",
                                )
                            ],
                        ]
                    ),
                )
            else:
                await message.answer(
                    "❌ Failed to update login token. Please try again."
                )
        else:
            # Continue with template creation flow
            await _complete_template_configuration(message, state, login_token)

    except Exception as e:
        logger.error(f"Error updating login token: {e}")
        await message.answer("❌ An error occurred. Please try again.")

    if edit_mode:
        await state.clear()


async def _complete_template_configuration(
    message: Message, state: FSMContext, login_token: str
):
    """Complete the template-based configuration creation"""
    try:
        data = await state.get_data()
        service_name = data.get("service_name")
        base_url = data.get("base_url")
        template_id = data.get("template_id")

        if not all([service_name, base_url, template_id]):
            await message.answer("❌ Missing configuration data. Please start over.")
            await state.clear()
            return

        template_service = get_template_service()
        template = template_service.get_template(template_id)

        if not template:
            await message.answer("❌ Template not found.")
            await state.clear()
            return

        # Create configuration from template
        config = APIConfiguration(
            service_name=service_name,
            display_name=template.name,
            description=template.description,
            base_url=base_url,
            endpoints=template.endpoints,
            credentials=APICredentials(
                login_token=login_token,
                session_cookies=(
                    template.credentials_template.session_cookies
                    if template.credentials_template
                    else {}
                ),
                headers=(
                    template.credentials_template.headers
                    if template.credentials_template
                    else {}
                ),
            ),
            source=ConfigSource.ADMIN_PANEL,
            enabled=True,
            category=template.category.value if template.category else "general",
            tags=template.tags or [],
            environment="production",
            version="1.0",
        )

        # Save configuration
        api_service = get_api_config_service()
        success = await api_service.save_api_config(
            config, source=ConfigSource.ADMIN_PANEL, user_id=str(message.from_user.id)
        )

        if success:
            await message.answer(
                f"🎉 <b>Configuration Created Successfully!</b>\n\n"
                f"<b>Service:</b> {service_name}\n"
                f"<b>Template:</b> {template.name}\n"
                f"<b>Base URL:</b> {base_url}\n"
                f"<b>Status:</b> 🟢 Active\n\n"
                f"Your API configuration is ready to use!",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🧪 Test Configuration",
                                callback_data=f"api_config_test:{service_name}",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="✏️ Edit Configuration",
                                callback_data=f"api_config_edit:{service_name}",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="📋 View Details",
                                callback_data=f"api_config_view:{service_name}",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="⬅️ Back to Main", callback_data="api_config_main"
                            )
                        ],
                    ]
                ),
            )
        else:
            await message.answer("❌ Failed to save configuration. Please try again.")

    except Exception as e:
        logger.error(f"Error completing template configuration: {e}")
        await message.answer("❌ An error occurred while creating the configuration.")

    await state.clear()


# Add missing handlers for manual configuration creation


@router.callback_query(F.data == "api_config_add_manual")
@admin_required
@error_handler
async def callback_api_config_add_manual(callback: CallbackQuery, state: FSMContext):
    """Start manual API configuration creation"""
    text = "➕ <b>Manual API Configuration</b>\n\n"
    text += "Create a new API configuration from scratch.\n\n"
    text += "📝 <b>Steps:</b>\n"
    text += "1. Service name (unique identifier)\n"
    text += "2. Base URL\n"
    text += "3. Authentication details\n"
    text += "4. Endpoints (optional)\n\n"
    text += "Please enter a unique service name:"

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="api_config_main")],
        [
            InlineKeyboardButton(
                text="🎯 Use Template Instead", callback_data="api_config_template"
            )
        ],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )

    await state.set_state(APIConfigStates.WAITING_SERVICE_NAME)


@router.message(APIConfigStates.WAITING_SERVICE_NAME)
@admin_required
@error_handler
async def message_api_config_service_name(message: Message, state: FSMContext):
    """Handle service name input for manual configuration"""
    service_name = message.text.strip().lower()
    error_service = get_error_service()

    # Validate service name
    if not service_name or not service_name.replace("_", "").isalnum():
        error_info = error_service.get_error_info("INVALID_SERVICE_NAME")
        error_msg = error_service.format_error_message(error_info)

        await message.answer(
            error_msg,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel", callback_data="api_config_main"
                        )
                    ]
                ]
            ),
        )
        return

    # Check for duplicates
    api_service = get_api_config_service()
    existing_config = await api_service.get_api_config(service_name)

    if existing_config and existing_config.source in [
        ConfigSource.ADMIN_PANEL,
        ConfigSource.DATABASE,
    ]:
        error_info = error_service.get_error_info("DUPLICATE_SERVICE_NAME")
        error_msg = error_service.format_error_message(error_info)

        await message.answer(
            error_msg,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="✏️ Edit Existing",
                            callback_data=f"api_config_edit:{service_name}",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔄 Try Different Name",
                            callback_data="api_config_add_manual",
                        )
                    ],
                ]
            ),
        )
        return

    # Store service name and ask for base URL
    await state.update_data(service_name=service_name, manual_creation=True)

    text = f"✅ Service name: `{service_name}`\n\n"
    text += "🌐 Next, enter the base API URL:\n"
    text += "Example: https://api.example.com\n\n"
    text += "💡 This should be the root URL of your API without any specific endpoints."

    keyboard = [
        [InlineKeyboardButton(text="❌ Cancel", callback_data="api_config_main")]
    ]

    await message.answer(
        text,
        parse_mode="Markdown",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )

    await state.set_state(APIConfigStates.WAITING_BASE_URL)


@router.callback_query(F.data.startswith("api_config_view:"))
@admin_required
@error_handler
async def callback_api_config_view(callback: CallbackQuery, state: FSMContext):
    """Show detailed view of API configuration"""
    service_name = callback.data.split(":", 1)[1]
    api_service = get_api_config_service()

    config = await api_service.get_api_config(service_name)
    if not config:
        await callback.answer("❌ Configuration not found", show_alert=True)
        return

    # Status and source indicators
    status_icon = "🟢" if config.enabled else "🔴"
    source_icons = {
        ConfigSource.ADMIN_PANEL: "👤",
        ConfigSource.DATABASE: "💾",
        ConfigSource.ENVIRONMENT: "🌍",
        ConfigSource.DEFAULT: "⚙️",
    }
    source_icon = source_icons.get(config.source, "❓")

    text = f"📋 <b>Configuration Details</b>\n\n"
    text += f"<b>Service:</b> {_html.escape(config.display_name or service_name)}\n"
    text += (
        f"<b>Status:</b> {status_icon} {'Active' if config.enabled else 'Inactive'}\n"
    )
    text += f"<b>Source:</b> {source_icon} {config.source.value.title()}\n"
    text += f"<b>Base URL:</b> {_html.escape(config.base_url)}\n"

    if config.description:
        text += f"<b>Description:</b> {_html.escape(config.description)}\n"

    # Credentials info (masked)
    text += f"\n🔐 <b>Authentication:</b>\n"
    if config.credentials and config.credentials.login_token:
        masked_token = (
            config.credentials.login_token[:8] + "..."
            if len(config.credentials.login_token) > 8
            else "***"
        )
        text += f"• Token: {masked_token}\n"
    else:
        text += f"• Token: Not configured\n"

    if config.credentials and config.credentials.session_cookies:
        text += f"• Cookies: {len(config.credentials.session_cookies)} configured\n"
    else:
        text += f"• Cookies: None\n"

    if config.credentials and config.credentials.headers:
        text += f"• Headers: {len(config.credentials.headers)} configured\n"
    else:
        text += f"• Headers: Default\n"

    # Endpoints info
    text += f"\n🔗 <b>Endpoints:</b>\n"
    if config.endpoints:
        for endpoint_name, endpoint in config.endpoints.items():
            text += f"• {endpoint_name}: {endpoint.method} {endpoint.url}\n"
    else:
        text += f"• No endpoints configured\n"

    keyboard = [
        [
            InlineKeyboardButton(
                text="✏️ Edit", callback_data=f"api_config_edit:{service_name}"
            ),
            InlineKeyboardButton(
                text="🧪 Test", callback_data=f"api_config_test:{service_name}"
            ),
        ],
        [
            InlineKeyboardButton(
                text="🔍 Validate", callback_data=f"api_config_validate:{service_name}"
            ),
            InlineKeyboardButton(
                text="📤 Export",
                callback_data=f"api_config_export_single:{service_name}",
            ),
        ],
        [InlineKeyboardButton(text="⬅️ Back", callback_data="api_config_main")],
    ]

    await callback.message.edit_text(
        text,
        parse_mode="HTML",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )


@router.callback_query(F.data == "api_config_profile_assign")
@admin_required
@error_handler
async def callback_profile_assignment_overview(
    callback: CallbackQuery, state: FSMContext
):
    """Show authentication profile assignment overview"""
    try:
        api_service = get_api_config_service()

        # Get profile usage summary
        usage_summary = await api_service.get_profile_usage_summary()

        text = "🔗 <b>Authentication Profile Assignments</b>\n\n"

        if not usage_summary:
            text += "❌ No data available"
        else:
            total_configs = usage_summary.get("total_configs", 0)
            configs_with_profiles = usage_summary.get("configs_with_profiles", 0)
            configs_without_profiles = usage_summary.get("configs_without_profiles", 0)
            adoption_rate = usage_summary.get("profile_adoption_rate", 0)

            text += f"📊 <b>Overview:</b>\n"
            text += f"• Total API Configurations: {total_configs}\n"
            text += f"• Using Profiles: {configs_with_profiles}\n"
            text += f"• Using Local Auth: {configs_without_profiles}\n"
            text += f"• Profile Adoption Rate: {adoption_rate:.1f}%\n\n"

            profile_usage = usage_summary.get("profile_usage", {})
            if profile_usage:
                text += "🔐 <b>Profile Usage:</b>\n"
                for profile_id, usage_info in profile_usage.items():
                    profile_name = usage_info.get("display_name", "Unknown")
                    api_count = len(usage_info.get("apis", []))
                    text += f"• {profile_name}: {api_count} APIs\n"
                text += "\n"

            text += "💡 <b>Quick Actions:</b>\n"
            text += "• Create new authentication profiles\n"
            text += "• Assign profiles to API configurations\n"
            text += "• View detailed usage reports"

        keyboard = [
            [
                InlineKeyboardButton(
                    text="🔐 Manage Profiles", callback_data="auth_profiles_main"
                ),
                InlineKeyboardButton(
                    text="📊 Detailed Report", callback_data="auth_profile_usage"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🔄 Refresh", callback_data="api_config_profile_assign"
                ),
                InlineKeyboardButton(text="⬅️ Back", callback_data="api_config_main"),
            ],
        ]

        await callback.message.edit_text(
            text,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
        )

    except Exception as e:
        logger.error(f"Failed to show profile assignment overview: {e}")
        await callback.answer(
            "❌ Failed to load profile assignment data", show_alert=True
        )
