"""
Tests for the shared API system

Comprehensive tests to ensure the shared API system works correctly
and maintains backward compatibility with existing API v1 functionality.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

# Import shared API components
from shared_api.config.api_config import (
    APIConfiguration,
    EndpointConfiguration,
    AuthenticationConfiguration,
    TimeoutConfiguration,
    RetryConfiguration,
)
from shared_api.config.client_factory import APIClientFactory, api_client_factory
from shared_api.config.registry import APIRegistry, api_registry
from shared_api.http.client import ConfigurableHTTPClient
from shared_api.examples.api_v1_config import create_api_v1_configuration
from shared_api.examples.new_api_config import create_new_api_configuration
from shared_api.compatibility import APIv1CompatibilityClient, get_external_api_service
from shared_api.core.constants import HTTPMethod, AuthenticationType
from shared_api.core.exceptions import ConfigurationError, HTTPClientError


class TestAPIConfiguration:
    """Test API configuration creation and validation"""
    
    def test_basic_configuration_creation(self):
        """Test creating a basic API configuration"""
        config = APIConfiguration(
            name="test_api",
            base_url="https://api.test.com",
            endpoints={
                "test_endpoint": EndpointConfiguration(
                    name="test_endpoint",
                    path="/test",
                    method=HTTPMethod.GET
                )
            }
        )
        
        assert config.name == "test_api"
        assert config.base_url == "https://api.test.com"
        assert "test_endpoint" in config.endpoints
        assert config.validate() is True
    
    def test_configuration_validation(self):
        """Test configuration validation"""
        # Test invalid URL
        with pytest.raises(Exception):  # Should raise ValidationError
            APIConfiguration(
                name="test",
                base_url="invalid-url",
                endpoints={}
            )
        
        # Test empty name
        with pytest.raises(Exception):  # Should raise ValidationError
            APIConfiguration(
                name="",
                base_url="https://api.test.com",
                endpoints={}
            )
    
    def test_endpoint_url_generation(self):
        """Test endpoint URL generation"""
        config = APIConfiguration(
            name="test_api",
            base_url="https://api.test.com",
            endpoints={
                "users": EndpointConfiguration(
                    name="users",
                    path="/users",
                    method=HTTPMethod.GET
                )
            }
        )
        
        url = config.get_endpoint_url("users")
        assert url == "https://api.test.com/users"
    
    def test_authentication_headers(self):
        """Test authentication header generation"""
        auth = AuthenticationConfiguration(
            type=AuthenticationType.API_KEY,
            api_key="test-key",
            api_key_header="X-API-Key"
        )
        
        config = APIConfiguration(
            name="test_api",
            base_url="https://api.test.com",
            authentication=auth,
            endpoints={}
        )
        
        headers = config.get_auth_headers()
        assert headers["X-API-Key"] == "test-key"


class TestAPIClientFactory:
    """Test API client factory functionality"""
    
    def test_client_creation(self):
        """Test creating clients from configuration"""
        factory = APIClientFactory()
        
        config = APIConfiguration(
            name="test_api",
            base_url="https://api.test.com",
            endpoints={
                "test": EndpointConfiguration(
                    name="test",
                    path="/test",
                    method=HTTPMethod.GET
                )
            }
        )
        
        client = factory.create_client(config)
        assert isinstance(client, ConfigurableHTTPClient)
        assert client.config == config
    
    def test_configuration_registration(self):
        """Test registering and using configurations"""
        factory = APIClientFactory()
        
        config = APIConfiguration(
            name="registered_api",
            base_url="https://api.test.com",
            endpoints={}
        )
        
        factory.register_configuration(config)
        client = factory.create_client("registered_api")
        assert isinstance(client, ConfigurableHTTPClient)
    
    def test_api_v1_configuration_creation(self):
        """Test creating API v1 configuration"""
        factory = APIClientFactory()
        
        config = factory.create_api_v1_configuration(
            base_url="https://test.com/api",
            login_token="test-token"
        )
        
        assert config.name == "api_v1"
        assert config.base_url == "https://test.com/api"
        assert "list_items" in config.endpoints
        assert "cart_view" in config.endpoints
        assert config.auth_config.type == AuthenticationType.BEARER_TOKEN


class TestAPIRegistry:
    """Test API registry functionality"""
    
    def test_api_registration(self):
        """Test registering APIs in the registry"""
        registry = APIRegistry()
        
        config = APIConfiguration(
            name="test_registry_api",
            base_url="https://api.test.com",
            endpoints={}
        )
        
        registry.register_api(config)
        
        # Test retrieval
        retrieved_config = registry.get_configuration("test_registry_api")
        assert retrieved_config.name == "test_registry_api"
        
        # Test client creation
        client = registry.get_client("test_registry_api")
        assert isinstance(client, ConfigurableHTTPClient)
    
    def test_api_listing(self):
        """Test listing registered APIs"""
        registry = APIRegistry()
        
        config1 = APIConfiguration(name="api1", base_url="https://api1.com", endpoints={})
        config2 = APIConfiguration(name="api2", base_url="https://api2.com", endpoints={})
        
        registry.register_api(config1)
        registry.register_api(config2)
        
        apis = registry.list_apis()
        assert len(apis) == 2
        assert any(api["name"] == "api1" for api in apis)
        assert any(api["name"] == "api2" for api in apis)


class TestCompatibilityLayer:
    """Test backward compatibility with existing API v1 code"""
    
    def test_compatibility_client_creation(self):
        """Test creating compatibility client"""
        client = get_external_api_service(
            base_url="https://test.com/api",
            login_token="test-token"
        )
        
        assert isinstance(client, APIv1CompatibilityClient)
        assert client.base_url == "https://test.com/api"
        assert client.login_token == "test-token"
    
    @pytest.mark.asyncio
    async def test_compatibility_client_methods(self):
        """Test that compatibility client provides expected methods"""
        client = get_external_api_service()
        
        # Check that all expected methods exist
        assert hasattr(client, 'list_items')
        assert hasattr(client, 'add_to_cart')
        assert hasattr(client, 'view_cart')
        assert hasattr(client, 'delete_from_cart')
        assert hasattr(client, 'get_user_info')
        assert hasattr(client, 'checkout')
        assert hasattr(client, 'check_order')
        
        # Test async context manager
        async with client:
            pass  # Should not raise any errors


class TestSharedAPIExamples:
    """Test the example configurations"""
    
    def test_api_v1_example_configuration(self):
        """Test API v1 example configuration"""
        config = create_api_v1_configuration(
            base_url="https://test.com/api",
            login_token="test-token"
        )
        
        assert config.name == "api_v1"
        assert config.base_url == "https://test.com/api"
        assert len(config.endpoints) > 0
        assert "list_items" in config.endpoints
        assert config.auth_config.type == AuthenticationType.BEARER_TOKEN
    
    def test_new_api_example_configuration(self):
        """Test new API example configuration"""
        config = create_new_api_configuration(
            api_name="test_new_api",
            base_url="https://newapi.test.com",
            api_key="test-key"
        )
        
        assert config.name == "test_new_api"
        assert config.base_url == "https://newapi.test.com"
        assert len(config.endpoints) > 0
        assert "list_users" in config.endpoints
        assert config.auth_config.type == AuthenticationType.API_KEY


class TestErrorHandling:
    """Test error handling in the shared API system"""
    
    def test_configuration_errors(self):
        """Test configuration error handling"""
        factory = APIClientFactory()
        
        # Test unknown configuration
        with pytest.raises(ConfigurationError):
            factory.create_client("nonexistent_api")
        
        # Test unknown client type
        config = APIConfiguration(
            name="test",
            base_url="https://api.test.com",
            endpoints={}
        )
        
        with pytest.raises(ConfigurationError):
            factory.create_client(config, client_type="nonexistent_type")


class TestIntegration:
    """Integration tests for the complete shared API system"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_api_usage(self):
        """Test complete API usage flow"""
        # Create configuration
        config = create_new_api_configuration(
            api_name="integration_test_api",
            base_url="https://httpbin.org",  # Use httpbin for testing
            api_key="test-key"
        )
        
        # Add a simple endpoint for testing
        config.add_endpoint(EndpointConfiguration(
            name="get_json",
            path="/json",
            method=HTTPMethod.GET,
            description="Get JSON response"
        ))
        
        # Create client and make request
        factory = APIClientFactory()
        
        # Note: This would require actual HTTP mocking for real tests
        # For now, just test that the client can be created and configured
        client = factory.create_client(config)
        assert isinstance(client, ConfigurableHTTPClient)
        assert client.config.name == "integration_test_api"
    
    def test_configuration_serialization(self):
        """Test configuration serialization and deserialization"""
        original_config = create_api_v1_configuration()
        
        # Convert to dict
        config_dict = original_config.to_dict()
        assert isinstance(config_dict, dict)
        assert config_dict["name"] == "api_v1"
        
        # Convert back to configuration
        restored_config = APIConfiguration.from_dict(config_dict)
        assert restored_config.name == original_config.name
        assert restored_config.base_url == original_config.base_url
        assert len(restored_config.endpoints) == len(original_config.endpoints)


# Test fixtures and utilities
@pytest.fixture
def sample_api_config():
    """Fixture providing a sample API configuration"""
    return APIConfiguration(
        name="sample_api",
        base_url="https://api.sample.com",
        endpoints={
            "test_endpoint": EndpointConfiguration(
                name="test_endpoint",
                path="/test",
                method=HTTPMethod.GET
            )
        },
        authentication=AuthenticationConfiguration(
            type=AuthenticationType.API_KEY,
            api_key="sample-key"
        )
    )


@pytest.fixture
def mock_http_response():
    """Fixture providing a mock HTTP response"""
    mock_response = MagicMock()
    mock_response.status = 200
    mock_response.headers = {"Content-Type": "application/json"}
    mock_response.json.return_value = {"success": True, "data": "test"}
    mock_response.text.return_value = '{"success": true, "data": "test"}'
    return mock_response


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
